import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import '../../../controller/controller.dart';

// متغير controller مشترك
final HomeController controller = HomeController();

/// ويدجت النص المخصص
Widget txtStyle(
    {required String txt,
    Color? color,
    double size = 0,
    int maxLines = 1,
    TextAlign align = TextAlign.center,
    TextDirection txtDirection = TextDirection.rtl}) {
  if (color == null) {
    color = AppColors.textSecondary;
  }
  if (size == 0) {
    size = controller.sized * 0.0125;
  }
  return Text(
    txt,
    textAlign: align,
    maxLines: maxLines,
    textDirection: txtDirection,
    style: TextStyle(
        height: controller.sizedHight * 0.00125,
        color: color,
        fontSize: size,
        fontWeight: FontWeight.bold),
  );
}

/// ويدجت الأيقونة المخصصة
Widget iconStyle({required IconData icon, Color? color, double size = 0}) {
  if (color == null) {
    color = AppColors.textSecondary;
  }
  if (size == 0) {
    size = controller.sized * 0.03;
  }

  return Icon(
    icon,
    color: color,
    size: size,
  );
}
