import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import '../../../controller/controller.dart';

// متغير controller مشترك
final HomeController controller = HomeController();

/// حاوية خيارات الصفحة
Widget containerPageOption({
  required Widget content,
  double ver = 0,
}) {
  return Container(
    padding: EdgeInsets.symmetric(
        horizontal: controller.sizedWidth * 0.01,
        vertical: controller.sizedHight * ver),
    margin: EdgeInsets.symmetric(
        horizontal: controller.sizedWidth * 0.055,
        vertical: controller.sizedHight * 0.0075),
    // width: controller.sizedWidth * 0.85,
    // height: controller.sizedHight * 0.4,
    decoration: BoxDecoration(
      gradient: AppColors.cardGradient,
      borderRadius: BorderRadius.all(Radius.circular(35)),
      border: Border.all(
        color: AppColors.border,
        width: 1.0,
      ),
    ),
    child: content,
  );
}

/// حاوية خيارات الأيقونات
Widget containerIconsOption({
  required Widget content,
  double ver = 0,
  double radius = 35,
  Color? color,
  EdgeInsets margin = EdgeInsets.zero,
  EdgeInsets padding = EdgeInsets.zero,
}) {
  return Container(
    padding: padding == EdgeInsets.zero
        ? EdgeInsets.symmetric(
            horizontal: controller.sizedWidth * 0.01,
            vertical: controller.sizedHight * ver)
        : padding,
    margin: margin,
    // width: controller.sizedWidth * 0.85,
    // height: controller.sizedHight * 0.4,
    decoration: BoxDecoration(
      gradient: color == null ? AppColors.cardGradient : null,
      color: color,
      borderRadius: BorderRadius.all(Radius.circular(radius)),
      border: Border.all(
        color: AppColors.border,
        width: 1.0,
      ),
    ),
    child: content,
  );
}

/// ويدجت نمط الانزلاق للصفحة
Widget pageSlidingStyle() => FractionallySizedBox(
      widthFactor: controller.sizedWidth * 0.0008,
      child: Container(
        margin: EdgeInsets.symmetric(
            horizontal: controller.sizedWidth * 0.01,
            vertical: controller.sizedHight * 0.01),
        height: controller.sizedHight * 0.005,
        decoration: BoxDecoration(
          color: AppColors.textSecondary.withOpacity(0.3),
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
