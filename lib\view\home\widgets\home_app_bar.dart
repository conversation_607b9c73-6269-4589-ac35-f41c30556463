import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/themes/app_colors.dart';

/// ويدجت شريط التطبيق العلوي للصفحة الرئيسية
class HomeAppBar extends StatelessWidget {
  final HomeController controller;
  final bool menublur;
  final VoidCallback onMenuTap;
  final Function(String) onMenuSelected;

  const HomeAppBar({
    Key? key,
    required this.controller,
    required this.menublur,
    required this.onMenuTap,
    required this.onMenuSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      expandedHeight: controller.sizedHight * 0.35,
      flexibleSpace: FlexibleSpaceBar(
        centerTitle: true,
        title: Container(
          padding: EdgeInsets.symmetric(
            horizontal: controller.sizedWidth * 0.04,
            vertical: controller.sizedHight * 0.01,
          ),
          decoration: BoxDecoration(
            color: AppColors.backgroundColor2.withOpacity(0.8),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: AppColors.border,
              width: 1.0,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة القائمة
              GestureDetector(
                onTap: onMenuTap,
                child: Container(
                  padding: EdgeInsets.all(controller.sized * 0.008),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Icon(
                    Icons.menu,
                    color: AppColors.primary,
                    size: controller.sized * 0.025,
                  ),
                ),
              ),
              
              SizedBox(width: controller.sizedWidth * 0.03),
              
              // اسم المنزل
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    txtStyle(
                      txt: controller.home.value,
                      color: AppColors.textPrimary,
                      size: controller.sized * 0.016,
                      align: TextAlign.center,
                    ),
                    SizedBox(height: controller.sizedHight * 0.002),
                    txtStyle(
                      txt: controller.homeType.value,
                      color: AppColors.textSecondary,
                      size: controller.sized * 0.012,
                      align: TextAlign.center,
                    ),
                  ],
                ),
              ),
              
              SizedBox(width: controller.sizedWidth * 0.03),
              
              // حالة الاتصال والمفتاح
              _buildConnectionStatus(),
            ],
          ),
        ),
        background: _buildBackground(context),
      ),
      pinned: true,
      floating: false,
      snap: false,
    );
  }

  /// بناء خلفية شريط التطبيق
  Widget _buildBackground(BuildContext context) {
    return Container(
      child: controller.homeImage.value.contains('com.example.zaen')
          ? Image.file(
              File(controller.homeImage.value),
              color: AppColors.subtitleColor.withOpacity(0.3),
              colorBlendMode: BlendMode.darken,
              fit: BoxFit.cover,
              filterQuality: FilterQuality.high,
            )
          : Image.asset(
              controller.homeImage.value,
              color: AppColors.subtitleColor.withOpacity(0.3),
              colorBlendMode: BlendMode.darken,
              fit: BoxFit.cover,
              filterQuality: FilterQuality.high,
            ),
    );
  }

  /// بناء حالة الاتصال
  Widget _buildConnectionStatus() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: controller.sizedWidth * 0.02,
      ),
      child: controller.devices['home'] == false
          ? Padding(
              padding: EdgeInsets.symmetric(
                horizontal: controller.sizedWidth * 0.01,
                vertical: controller.sizedHight * 0.015,
              ),
              child: Text(
                'غير متصل',
                textDirection: TextDirection.rtl,
                style: TextStyle(
                  color: Color.fromARGB(255, 238, 19, 3),
                  fontSize: controller.sized * 0.0135,
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          : switchStyle(
              value: controller.homeState,
              onChanged: (val) {
                // commandHome(val!);
              },
            ),
    );
  }
}
