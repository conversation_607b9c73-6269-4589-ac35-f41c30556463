import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:sqflite/sqflite.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/themes/app_colors.dart';

// استيراد ملفات الإعدادات المنظمة
import 'widgets/settings_home_page.dart';
import 'pages/add_room_page.dart';
import 'pages/devices_connect_page.dart';
import 'pages/routine_words_page.dart';
import 'pages/tasks_page.dart';
import 'helpers/settings_helpers.dart';

/// ويدجت الإعدادات الرئيسي
Widget setting({context, setState}) {
  return GetBuilder<HomeController>(
    builder: (controller) => SettingsHomePage(
      controller: controller,
      onRoutineWordsPressed: () {
        RoutineWordsPage.show(context, setState);
      },
      onTasksPressed: () {
        TasksPage.show(context, setState);
      },
      onAddRoomPressed: () {
        AddRoomPage.show(context);
      },
      onDevicesConnectPressed: () {
        DevicesConnectPage.show(context, setState);
      },
      onAssetPressed: () async {
        await SettingsHelpers.handleAssetSelection(controller);
      },
      onRollPressed: () async {
        await SettingsHelpers.handleRollAction(controller);
      },
      onCameraPressed: () async {
        await SettingsHelpers.handleCameraAction(controller);
      },
      onEditNamesPressed: (String? newName) {
        SettingsHelpers.handleEditNames(controller, newName);
      },
      onEditPrivNamePressed: (bool? isPrivate, String? name) {
        SettingsHelpers.handleEditPrivName(controller, isPrivate, name);
      },
    ),
  );
}
