import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/config.dart';
import 'package:zaen/shared/components/constants.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/settings/settings.dart';

Widget HomePage({
  String? roomPrivName,
  var image,
  String? homeType,
  Function()? routinWords,
  Function()? tasks,
  Function()? addRoom,
  Function()? nunDevices,
  required Function() del,
  required Function() asset,
  required Function() roll,
  required Function() camera,
  required Function(String?) editNames,
  required Function(bool?, String?) editPrivName,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  TextEditingController editPriv = TextEditingController(
    text: roomPrivName != 'x' ? roomPrivName : 'X',
  );
  bool privN = false;

  return GetBuilder<SettingsController>(
    builder: (settingsController) => GestureDetector(
      onTap: () {
        if (privN) {
          if (editPriv.text == '' ||
              editPriv.text == null ||
              editPriv.text == 'X' ||
              editPriv.text == 'x') {
            editPriv.text = roomPrivName != null ? roomPrivName : 'X';
            privN = false;
          } else {
            for (var i = 0; i < editPriv.text.length; i++) {
              if (arabic.contains(editPriv.text[i]) ||
                  editPriv.text[i].isNumericOnly) {
                privN = true;
              } else {
                editPriv.text = roomPrivName != null ? roomPrivName : 'X';
                privN = false;
                break;
              }
            }

            if (privN) {
              editPrivName(privN, editPriv.text);
              privN = false;
            }
          }
        }
        FocusManager.instance.primaryFocus?.unfocus();
      },
      child: PageView(
          scrollDirection: Axis.vertical,
          // shrinkWrap: true,
          onPageChanged: (i) {
            FocusManager.instance.primaryFocus?.unfocus();
            editPriv.text = roomPrivName != null ? roomPrivName : 'X';
          },
          physics: BouncingScrollPhysics(),
          children: [
            SingleChildScrollView(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: sizedWidth * 0.03),
                child: Directionality(
                  textDirection: TextDirection.ltr,
                  child: Column(
                    children: [
                      Container(
                        width: sizedWidth * 0.85,
                        child: TextFormField(
                          controller: editPriv,
                          maxLength: 15,
                          showCursor: true,
                          cursorColor: AppColors.primary,
                          textDirection: TextDirection.rtl,
                          style: TextStyle(
                            color: AppColors.textPrimary,
                            fontSize: sized * 0.015,
                            fontWeight: FontWeight.w500,
                          ),
                          onChanged: (i) {
                            privN = true;
                          },
                          onEditingComplete: () {
                            FocusManager.instance.primaryFocus?.unfocus();
                            if (editPriv.text == '' ||
                                editPriv.text == null ||
                                editPriv.text == 'X' ||
                                editPriv.text == 'x') {
                              editPriv.text =
                                  roomPrivName != null ? roomPrivName : 'X';
                              privN = false;
                            } else if (editPriv.text != roomPrivName) {
                              for (var i = 0; i < editPriv.text.length; i++) {
                                if (arabic.contains(editPriv.text[i]) ||
                                    editPriv.text[i].isNumericOnly) {
                                  privN = true;
                                } else {
                                  editPriv.text =
                                      roomPrivName != null ? roomPrivName : 'X';
                                  privN = false;
                                  break;
                                }
                              }
                              if (privN) {
                                editPrivName(privN, editPriv.text);
                                privN = false;
                              }
                            }
                          },
                          decoration: InputDecoration(
                            hintText: 'اسم النظام الخاص',
                            hintStyle: TextStyle(
                              color: AppColors.textHint,
                              fontSize: sized * 0.014,
                              fontWeight: FontWeight.normal,
                            ),
                            filled: true,
                            fillColor: AppColors.surface,
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: sizedWidth * 0.04,
                              vertical: sizedHeight * 0.015,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.border,
                                width: 1.0,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.border,
                                width: 1.0,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: AppColors.primary,
                                width: 2.0,
                              ),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),

                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: routinWords,
                          child: Row(children: [
                            iconStyle(
                                icon: Icons.menu_open_rounded,
                                size: controller.sized * 0.02),
                            Expanded(
                                child: txtStyle(
                                    txt: 'الكلمات الروتينية',
                                    align: TextAlign.start))
                          ]),
                        ),
                      ),
                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: tasks,
                          child: Row(children: [
                            iconStyle(
                                icon: Icons.more_time_rounded,
                                color: AppColors.warningColor,
                                size: controller.sized * 0.02),
                            Expanded(
                                child: txtStyle(
                                    txt: 'المهام المجدولة',
                                    align: TextAlign.start))
                          ]),
                        ),
                      ),
                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),
                      // مفتاح الوضع الداكن/الفاتح
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.only(
                              right: controller.sizedWidth * 0.01),
                          onPressed: (() {}),
                          child: Row(children: [
                            Obx(() {
                              final settingsController =
                                  Get.find<SettingsController>();
                              return themeSwitchStyle(
                                  onChanged: (val) {
                                    settingsController.toggleTheme();
                                  },
                                  value: settingsController.isDarkMode.value,
                                  size: controller.sized * 0.0007);
                            }),
                            Expanded(
                                child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                  Obx(() {
                                    final settingsController =
                                        Get.find<SettingsController>();
                                    return txtStyle(
                                        txt: settingsController.isDarkMode.value
                                            ? 'الوضع الداكن'
                                            : 'الوضع الفاتح',
                                        align: TextAlign.start);
                                  }),
                                  SizedBox(
                                    width: controller.sizedWidth * 0.02,
                                  ),
                                  Obx(() {
                                    final settingsController =
                                        Get.find<SettingsController>();
                                    return iconStyle(
                                        icon:
                                            settingsController.isDarkMode.value
                                                ? Icons.dark_mode
                                                : Icons.light_mode,
                                        color: AppColors.warningColor,
                                        size: controller.sized * 0.017);
                                  }),
                                ]))
                          ]),
                        ),
                      ),
                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: () {
                            Future<Map<String, String>> getDeviceInfo() async {
                              DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

                              if (Platform.isAndroid) {
                                AndroidDeviceInfo androidInfo =
                                    await deviceInfo.androidInfo;
                                return {
                                  'deviceName': androidInfo.device,
                                  'model': androidInfo.device, // اسم الطراز
                                };
                              }

                              if (Platform.isIOS) {
                                IosDeviceInfo iosInfo =
                                    await deviceInfo.iosInfo;
                                return {
                                  'deviceName': iosInfo.name, // اسم الجهاز
                                  'model':
                                      iosInfo.model, // الطراز (مثل iPhone X)
                                };
                              }

                              return {'error': 'غير معروف'};
                            }

                            getDeviceInfo().then((f) {
                              print(f);
                            });
                          },
                          child: Row(children: [
                            iconStyle(
                                icon: Icons.people_alt_rounded,
                                color: AppColors.warningColor,
                                size: controller.sized * 0.02),
                            Expanded(
                                child: txtStyle(
                                    txt: 'الاشخاص', align: TextAlign.start))
                          ]),
                        ),
                      ),
                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.only(
                              right: controller.sizedWidth * 0.01),
                          onPressed: (() {}),
                          child: Row(children: [
                            switchStyle(
                                onChanged: (val) {},
                                value: false,
                                size: controller.sized * 0.0007),
                            Expanded(
                                child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                  txtStyle(
                                      txt: 'التوقيت الصيفي',
                                      align: TextAlign.start),
                                  SizedBox(
                                    width: controller.sizedWidth * 0.02,
                                  ),
                                  iconStyle(
                                      icon: Icons.bedtime,
                                      color: AppColors.warningColor,
                                      size: controller.sized * 0.017),
                                ]))
                          ]),
                        ),
                      ),
                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.02),
                          onPressed: (() {}),
                          child: Row(children: [
                            iconStyle(
                                icon: Icons.menu_open_rounded,
                                size: controller.sized * 0.02),
                            Expanded(
                                child: txtStyle(
                                    txt: 'المنطقة : قلقيلية',
                                    align: TextAlign.start))
                          ]),
                        ),
                      ),
                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: sizedWidth * 0.025),
                          onPressed: () {
                            addRoom!();
                          },
                          child: Row(mainAxisSize: MainAxisSize.min, children: [
                            Icon(
                              Icons.add_rounded,
                              size: sized * 0.02,
                              color: AppColors.primaryColor.withOpacity(0.6),
                            ),
                            Expanded(child: SizedBox(width: double.infinity)),
                            Text(
                              'اضافه غرفة',
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                  fontSize: sized * 0.015,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textColor.withOpacity(0.6)),
                            ),
                          ]),
                        ),
                      ),
                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: sizedWidth * 0.025),
                          onPressed: nunDevices,
                          child: Row(mainAxisSize: MainAxisSize.min, children: [
                            Icon(
                              Icons.dns_rounded,
                              size: sized * 0.02,
                              color: AppColors.textColor.withOpacity(0.6),
                            ),
                            Expanded(child: SizedBox(width: double.infinity)),
                            Text(
                              'الملحقات المتصلة',
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                  fontSize: sized * 0.015,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textColor.withOpacity(0.6)),
                            ),
                          ]),
                        ),
                      ),
                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),
                      containerPageOption(
                        content: MaterialButton(
                          padding: EdgeInsets.symmetric(
                              horizontal: sizedWidth * 0.025),
                          onPressed: () {},
                          child: Row(mainAxisSize: MainAxisSize.min, children: [
                            Icon(
                              Icons.wifi_rounded,
                              size: sized * 0.02,
                              color: Colors.lightBlue.withOpacity(0.6),
                            ),
                            Expanded(child: SizedBox(width: double.infinity)),
                            Text(
                              'ادارة الشبكة و المستخدمين',
                              textDirection: TextDirection.rtl,
                              style: TextStyle(
                                  fontSize: sized * 0.015,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textColor.withOpacity(0.6)),
                            ),
                          ]),
                        ),
                      ),
                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),
                      containerPageOption(
                          ver: 0.015,
                          content: Column(
                            children: [
                              Container(
                                height: sizedHeight * 0.2,
                                width: sizedWidth * 0.85,
                                child: image!.contains('com.example.zaen')
                                    ? Image.file(
                                        File(image),
                                        color: AppColors.subtitleColor
                                            .withOpacity(0.2),
                                        colorBlendMode: BlendMode.darken,
                                        fit: BoxFit.cover,
                                        filterQuality: FilterQuality.high,
                                      )
                                    : Image.asset(
                                        "$image",
                                        color: AppColors.subtitleColor
                                            .withOpacity(0.2),
                                        colorBlendMode: BlendMode.darken,
                                        fit: BoxFit.cover,
                                        filterQuality: FilterQuality.high,
                                      ),
                              ),
                              SizedBox(height: sizedHeight * 0.01),
                              MaterialButton(
                                padding: EdgeInsets.symmetric(
                                    horizontal: sizedWidth * 0.01),
                                onPressed: asset,
                                child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.arrow_back_ios_new_rounded,
                                        size: sized * 0.017,
                                        color: AppColors.textColor
                                            .withOpacity(0.6),
                                      ),
                                      Expanded(
                                          child:
                                              SizedBox(width: double.infinity)),
                                      Text(
                                        'اختيار صوره من الموجود',
                                        textDirection: TextDirection.rtl,
                                        style: TextStyle(
                                            fontSize: sized * 0.015,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.textColor
                                                .withOpacity(0.6)),
                                      ),
                                    ]),
                              ),
                              SizedBox(height: sizedHeight * 0.01),
                              MaterialButton(
                                padding: EdgeInsets.symmetric(
                                    horizontal: sizedWidth * 0.01),
                                onPressed: roll,
                                child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.camera_rounded,
                                        size: sized * 0.017,
                                        color: AppColors.textColor
                                            .withOpacity(0.6),
                                      ),
                                      Expanded(
                                          child:
                                              SizedBox(width: double.infinity)),
                                      Text(
                                        'اختيار صوره من البوم الصور',
                                        textDirection: TextDirection.rtl,
                                        style: TextStyle(
                                            fontSize: sized * 0.015,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.textColor
                                                .withOpacity(0.6)),
                                      ),
                                    ]),
                              ),
                              SizedBox(height: sizedHeight * 0.01),
                              MaterialButton(
                                padding: EdgeInsets.symmetric(
                                    horizontal: sizedWidth * 0.01),
                                onPressed: camera,
                                child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.camera_alt_rounded,
                                        size: sized * 0.017,
                                        color: AppColors.textColor
                                            .withOpacity(0.6),
                                      ),
                                      Expanded(
                                          child:
                                              SizedBox(width: double.infinity)),
                                      Text(
                                        'التقاط صورة',
                                        textDirection: TextDirection.rtl,
                                        style: TextStyle(
                                            fontSize: sized * 0.015,
                                            fontWeight: FontWeight.bold,
                                            color: AppColors.textColor
                                                .withOpacity(0.6)),
                                      ),
                                    ]),
                              ),
                            ],
                          )),
                      SizedBox(
                        height: sizedHeight * 0.015,
                      ),
                      containerPageOption(
                        content: MaterialButton(
                            padding: EdgeInsets.zero,
                            onPressed: del,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                txtStyle(
                                    txt: 'إزالة النظام',
                                    color: AppColors.errorColor),
                              ],
                            )),
                      ),
                      SizedBox(
                        height: sizedHeight * 0.06,
                      ),
                    ],
                  ),
                ),
              ),
            )
          ]),
    ),
  );
}
