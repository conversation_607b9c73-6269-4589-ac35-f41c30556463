import 'dart:async';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mysql1/mysql1.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:zaen/modules/local/database.dart';
import 'package:zaen/modules/local/ip.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:uuid/uuid.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:zaen/modules/local/sql.dart';
import 'package:zaen/shared/themes/app_colors.dart';

// استيراد الكونترولرز المقسمة
import 'base_controller.dart';
import 'home_controller.dart';
import 'routine_controller.dart';
import 'favorite_controller.dart';

/// الكونترولر الرئيسي الذي يجمع جميع الوظائف
class HomeController extends GetxController {
  // بيانات المنزل والغرف
  Map rooms = {};
  Map alarms = {};
  Map roomData = {};
  Map addRoutine = {};
  List routines = [];
  List favorite = [];
  Results? tasks;
  Results? routineWords;
  RxString hostZain = ''.obs;
  RxString home = ''.obs;
  RxString homeType = ''.obs;
  RxString homeImage = ''.obs;
  String homeId = '';
  var homeState = null;
  bool reg = false;
  String apName = '';
  String apMAC = '';
  String sysMAC = '';
  var uuid;
  var deviceName;
  var deviceModel;
  List systems = [];
  String system = '';
  String wifi = '';
  Timer? _timer;
  Map devices = {'home': false};
  double sizedWidth = 0;
  double sizedHight = 0;
  double sized = 0;

  /// بدء تشغيل المنزل
  void start(homex, homeTypex, homeImagex, homeStatex, roomsx, homeIdx) {
    home.value = homex;
    homeType.value = homeTypex;
    homeImage.value = homeImagex;
    homeState = homeStatex;
    homeId = homeIdx;
    rooms = roomsx;
    update();
    print(rooms);
    print('55555555555555555555555555555');
  }

  /// الحصول على UUID الجهاز
  Future<void> _getDeviceUuid() async {
    final prefs = await SharedPreferences.getInstance();
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    uuid = prefs.getString('device_uuid');
    deviceName = prefs.getString('device_name');
    var deviceModel1 = prefs.getString('device_model');

    if (uuid == null) {
      uuid = Uuid().v4();
      await prefs.setString('device_uuid', uuid);
    }
    if (deviceName == null) {
      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceName = androidInfo.device;
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceName = iosInfo.name;
      } else {
        deviceName = 'غير معروف';
      }
      await prefs.setString('device_name', deviceName);
    }
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceModel = androidInfo.device;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceModel = iosInfo.model;
    } else {
      deviceModel = 'غير معروف';
    }
    if (deviceModel1 == null || deviceModel1 != deviceModel) {
      await prefs.setString('device_model', deviceModel);
    }
  }

  /// فحص حالة الاتصال
  Future<void> _checkConnectionStatus() async {
    try {
      final info = NetworkInfo();

      var wifiName = await info.getWifiName();
      final wifiBSSID = await info.getWifiBSSID();
      if (wifiName != wifi) {
        if (reg) {
          reg = false;
          if (systems.isEmpty) {
            Get.toNamed('wait');
          } else {
            Get.back();
          }
        }
        if (wifiName != null) {
          wifi = wifiName;
        } else {
          wifi = '';
        }
        update();
      }
      if (reg == false &&
          wifiName != null &&
          wifiName.toUpperCase().contains(wifiBSSID!.toUpperCase())) {
        wifi = wifiName;
        reg = true;
        apMAC = wifiBSSID;
        apName = wifiName.replaceAll('"', '').split('/')[0];
        Get.toNamed('reg');
      } else if (reg == false && wifiName != null && hostZain.isEmpty) {
        await findIp();
        if (hostZain.isNotEmpty) {
          await connect();
          await client.subscribe(uuid, MqttQos.atLeastOnce);

          final builder = MqttClientPayloadBuilder();
          builder.addString(uuid);
          client.publishMessage(
              'phone/ask', MqttQos.atLeastOnce, builder.payload!);
        }
      } else if (wifiName == null && hostZain.isNotEmpty) {
        hostZain.value = '';
      }
    } catch (e) {
      print('444444444444444444444444');
      print(e);
    }
  }

  @override
  void onInit() async {
    await _getDeviceUuid();
    await getSystems();
    print(systems);
    print('888888888888888888888888888');
    final info = NetworkInfo();
    final wifiName = await info.getWifiName();
    final wifiBSSID = await info.getWifiBSSID();
    if (wifiName != null) {
      wifi = wifiName;
      await findIp();
      if (hostZain.isNotEmpty) {
        await connect();
        await client.subscribe(uuid, MqttQos.atLeastOnce);

        final builder = MqttClientPayloadBuilder();
        builder.addString(uuid);
        client.publishMessage(
            'phone/ask', MqttQos.atLeastOnce, builder.payload!);
      }
    }
    if (systems.isNotEmpty && system != '') {
      try {
        if (wifi.isNotEmpty) {
          print('88888888');
          await findIp();
        }
        if (!reg) {
          if (hostZain.value.isEmpty) {
            final response =
                await http.get(Uri.parse('https://www.google.com'));
            if (response.statusCode == 200) {
              print('888888888888888888');
            }
          } else {
            await connect();
            await client.subscribe(uuid, MqttQos.atLeastOnce);

            final builder = MqttClientPayloadBuilder();
            builder.addString(uuid);
            client.publishMessage(
                'phone/ask', MqttQos.atLeastOnce, builder.payload!);
            await getDevices();
          }
        }
      } catch (e) {
        print('error 7777777777777');
      }
    } else {
      if (wifiName != null &&
          wifiName.toUpperCase().contains(wifiBSSID!.toUpperCase())) {
        wifi = wifiName;
        reg = true;
        apMAC = wifiBSSID;
        apName = wifiName.replaceAll('"', '').split('/')[0];
        Get.toNamed('reg');
      } else {
        Get.toNamed('wait');
      }
    }

    _timer = Timer.periodic(Duration(seconds: 7), (timer) {
      _checkConnectionStatus();
    });

    super.onInit();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }
}
