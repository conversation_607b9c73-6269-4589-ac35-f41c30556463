import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import '../../../controller/controller.dart';

// متغير controller مشترك
final HomeController controller = HomeController();

// متغير لتتبع حالة الـ bottom sheet
bool _isBottomSheetOpen = false;

/// ويدجت انزلاق الصفحة
Widget pageSlide({
  required Widget content,
  bool isClosing = false,
}) {
  return AnimatedPageSlide(content: content, isClosing: isClosing);
}

// نسخة بسيطة بدون حركات معقدة لتجنب المشاكل
Widget simplePageSlide({
  required Widget content,
}) {
  return TweenAnimationBuilder<double>(
    duration: const Duration(milliseconds: 300),
    tween: Tween<double>(begin: 0.0, end: 1.0),
    builder: (context, value, child) {
      return Transform.translate(
        offset: Offset(0, (1 - value) * MediaQuery.of(context).size.height * 0.3),
        child: Opacity(
          opacity: value,
          child: Container(
            height: double.infinity,
            width: double.infinity,
            margin: EdgeInsets.only(top: controller.sizedHight * 0.1),
            decoration: BoxDecoration(
              color: AppColors.backgroundColor2.withOpacity(0.989 * value),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(25.5)),
            ),
            child: content,
          ),
        ),
      );
    },
  );
}

// دالة مساعدة لإظهار pageSlide مع حركات جميلة
Future<T?> showAnimatedBottomSheet<T>({
  required BuildContext context,
  required Widget content,
  bool enableDrag = true,
  bool isDismissible = true,
}) async {
  // منع فتح عدة bottom sheets في نفس الوقت
  if (_isBottomSheetOpen) {
    return null;
  }

  _isBottomSheetOpen = true;

  try {
    final result = await showModalBottomSheet<T>(
      context: context,
      backgroundColor: Colors.transparent,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      isScrollControlled: true,
      builder: (context) => pageSlide(content: content),
    );

    return result;
  } finally {
    _isBottomSheetOpen = false;
  }
}

// دالة بديلة أكثر أماناً لـ showBottomSheet العادي
PersistentBottomSheetController? showSafeBottomSheet({
  required BuildContext context,
  required Widget content,
  bool enableDrag = true,
}) {
  if (_isBottomSheetOpen) {
    return null;
  }

  _isBottomSheetOpen = true;

  final controller = showBottomSheet(
    context: context,
    enableDrag: enableDrag,
    backgroundColor: Colors.transparent,
    builder: (context) => pageSlide(content: content),
  );

  // إعادة تعيين المتغير عند إغلاق الـ sheet
  controller.closed.then((_) {
    _isBottomSheetOpen = false;
  });

  return controller;
}

/// كلاس الانزلاق المتحرك للصفحة
class AnimatedPageSlide extends StatefulWidget {
  final Widget content;
  final bool isClosing;

  const AnimatedPageSlide({
    Key? key,
    required this.content,
    this.isClosing = false,
  }) : super(key: key);

  @override
  AnimatedPageSlideState createState() => AnimatedPageSlideState();
}

class AnimatedPageSlideState extends State<AnimatedPageSlide>
    with TickerProviderStateMixin {
  AnimationController? _slideController;
  AnimationController? _fadeController;
  AnimationController? _scaleController;
  Animation<Offset>? _slideAnimation;
  Animation<double>? _fadeAnimation;
  Animation<double>? _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    // تهيئة controllers
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );

    // تهيئة animations
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController!,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController!,
      curve: Curves.easeIn,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController!,
      curve: Curves.elasticOut,
    ));
  }

  void _startAnimations() {
    if (!widget.isClosing) {
      _slideController?.forward();
      _fadeController?.forward();
      _scaleController?.forward();
    } else {
      _slideController?.reverse();
      _fadeController?.reverse();
      _scaleController?.reverse();
    }
  }

  @override
  void dispose() {
    _slideController?.dispose();
    _fadeController?.dispose();
    _scaleController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_slideAnimation == null ||
        _fadeAnimation == null ||
        _scaleAnimation == null) {
      return Container(); // أو أي widget بديل
    }

    return AnimatedBuilder(
      animation: Listenable.merge(
          [_slideController!, _fadeController!, _scaleController!]),
      builder: (context, child) {
        return SlideTransition(
          position:
              _slideAnimation ?? AlwaysStoppedAnimation(const Offset(0, 1)),
          child: FadeTransition(
            opacity: _fadeAnimation ?? AlwaysStoppedAnimation(0.0),
            child: ScaleTransition(
              scale: _scaleAnimation ?? AlwaysStoppedAnimation(0.8),
              child: Container(
                height: double.infinity,
                width: double.infinity,
                margin: EdgeInsets.only(top: controller.sizedHight * 0.1),
                decoration: BoxDecoration(
                  color: AppColors.backgroundColor2
                      .withOpacity(0.989 * (_fadeAnimation?.value ?? 0.0)),
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(25.5)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black
                          .withOpacity(0.1 * (_fadeAnimation?.value ?? 0.0)),
                      blurRadius: 10,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: widget.content,
              ),
            ),
          ),
        );
      },
    );
  }
}
