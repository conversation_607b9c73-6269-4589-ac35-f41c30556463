import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/commands/room.dart';
import 'package:zaen/models/shortcuts.dart';

/// صفحة عرض الغرف
class RoomsPage extends StatelessWidget {
  final HomeController controller;

  const RoomsPage({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: BouncingScrollPhysics(),
      child: Column(
        children: [
          // عرض الغرف
          for (dynamic room in controller.rooms.values)
            shortcutRoom(
              sizedWidth: controller.sizedWidth,
              sizedHeight: controller.sizedHight,
              sized: controller.sized,
              connect: controller.devices[room['id']],
              onTap: () {
                controller.roomData = room;
                Get.toNamed('room');
              },
              roomName: room['pubName'],
              roomPrivName: room['privName'],
              roomState: room['state'],
              switchState: (val) {
                commandRoom(val!, room['id']);
              },
              image: room['image']
                      .toString()
                      .contains('com.example.zaen')
                  ? FileImage(File(room['image']))
                  : AssetImage("${room['image']}"),
            ),
          
          // مساحة إضافية في الأسفل
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}
