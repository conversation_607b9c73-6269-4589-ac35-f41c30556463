import 'package:flutter/material.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';

/// مساعدات الصفحة الرئيسية
class HomeHelpers {
  /// فحص التمرير
  static void checkScroll({
    required ScrollMetrics? currentMetrics,
    required ScrollMetricsNotification notification,
    required int currentPage,
    required Function(bool) onScrollChanged,
  }) {
    if (currentMetrics == null) {
      currentMetrics = notification.metrics;
    }

    if (currentPage == 1 && notification.metrics.axis == Axis.vertical) {
      int threshold = currentMetrics!.maxScrollExtent.toInt() - 75;
      int maxScroll = notification.metrics.maxScrollExtent.toInt();
      
      bool shouldScroll = threshold > maxScroll;
      onScrollChanged(shouldScroll);
    }
  }

  /// إعداد اشتراكات MQTT
  static void setupMqttSubscriptions(HomeController controller) {
    client.subscribe(
      controller.homeId + "/app/phone",
      MqttQos.atLeastOnce,
    );
    client.subscribe("edit", MqttQos.atLeastOnce);
    client.subscribe(controller.uuid, MqttQos.atLeastOnce);
  }

  /// تحديث أبعاد الشاشة
  static void updateScreenDimensions(
    BuildContext context,
    HomeController controller,
  ) {
    controller.sizedHight = MediaQuery.of(context).size.height;
    controller.sizedWidth = MediaQuery.of(context).size.width;
    controller.sized = controller.sizedHight + controller.sizedWidth;
  }

  /// التحقق من حالة الاتصال
  static bool isConnected(HomeController controller) {
    return controller.devices['home'] != false &&
           client.connectionStatus?.state.name == 'connected';
  }

  /// الحصول على نص حالة الاتصال
  static String getConnectionStatusText(HomeController controller) {
    return isConnected(controller) ? 'متصل' : 'غير متصل';
  }

  /// الحصول على لون حالة الاتصال
  static Color getConnectionStatusColor(HomeController controller) {
    return isConnected(controller) 
        ? const Color.fromARGB(255, 76, 175, 80)
        : const Color.fromARGB(255, 238, 19, 3);
  }
}
