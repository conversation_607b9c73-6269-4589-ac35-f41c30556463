import 'package:flutter/material.dart';

/// بناء انتقالات الصفحات المخصصة
class CustomPageTransitionBuilder extends PageTransitionsBuilder {
  @override
  Widget buildTransitions<T extends Object?>(
    PageRoute<T> route,
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    // استخدام انتقال مخصص بدلاً من الافتراضي
    const begin = Offset(1.0, 0.0);
    const end = Offset.zero;
    const curve = Curves.easeInOut;

    var tween = Tween(begin: begin, end: end).chain(
      CurveTween(curve: curve),
    );

    var fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: Curves.easeIn,
    ));

    return SlideTransition(
      position: animation.drive(tween),
      child: FadeTransition(
        opacity: fadeAnimation,
        child: child,
      ),
    );
  }
}

/// عرض صفحات متحركة
class AnimatedPageView extends StatefulWidget {
  final List<Widget> children;
  final PageController? controller;
  final ValueChanged<int>? onPageChanged;
  final Duration animationDuration;
  final Curve animationCurve;

  const AnimatedPageView({
    Key? key,
    required this.children,
    this.controller,
    this.onPageChanged,
    this.animationDuration = const Duration(milliseconds: 300),
    this.animationCurve = Curves.easeInOut,
  }) : super(key: key);

  @override
  AnimatedPageViewState createState() => AnimatedPageViewState();
}

class AnimatedPageViewState extends State<AnimatedPageView>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _animation;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = widget.controller ?? PageController();
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: widget.animationCurve,
    );
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _pageController.dispose();
    }
    _animationController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
    _animationController.forward(from: 0);
    widget.onPageChanged?.call(index);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return PageView.builder(
          controller: _pageController,
          onPageChanged: _onPageChanged,
          itemCount: widget.children.length,
          itemBuilder: (context, index) {
            return Transform.scale(
              scale: index == _currentIndex
                  ? 1.0
                  : 0.8 + (0.2 * _animation.value),
              child: Opacity(
                opacity: index == _currentIndex
                    ? 1.0
                    : 0.5 + (0.5 * _animation.value),
                child: widget.children[index],
              ),
            );
          },
        );
      },
    );
  }
}
