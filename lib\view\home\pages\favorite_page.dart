import 'package:flutter/material.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/view/home/<USER>/favorite/favorite.dart';

/// صفحة المفضلة
class FavoritePage extends StatelessWidget {
  final HomeController controller;
  final bool editFavorite;
  final VoidCallback onEditToggle;

  const FavoritePage({
    Key? key,
    required this.controller,
    required this.editFavorite,
    required this.onEditToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: BouncingScrollPhysics(),
      child: Column(
        children: [
          // عرض المفضلة
          favorite(
            context: context,
            editFavorite: editFavorite,
            onEditToggle: onEditToggle,
          ),
          
          // مساحة إضافية في الأسفل
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}
