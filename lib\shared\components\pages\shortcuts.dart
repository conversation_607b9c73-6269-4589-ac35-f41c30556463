import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import '../widgets/text_and_icons.dart';
import '../widgets/switches.dart';
import '../widgets/containers.dart';

// متغير controller مشترك
final HomeController controller = HomeController();

/// ويدجت نمط الاختصار
Widget shortCutStyle({
  required bool connect,
  required String PrivName,
  required String type,
  required Function() doubleTap,
  required Function() tapOnIcon,
  required List<Widget> content,
  bool? deviceState,
  required Function(bool?) switchState,
}) {
  return GestureDetector(
    onDoubleTap: doubleTap,
    child: Container(
      margin: EdgeInsets.symmetric(
        horizontal: controller.sizedWidth * 0.055,
        vertical: controller.sizedHight * 0.0075,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(30)),
        color: AppColors.backgroundColor3,
      ),
      child: Column(
        children: [
          // الرأس
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: controller.sizedWidth * 0.04,
              vertical: controller.sizedHight * 0.015,
            ),
            child: Row(
              children: [
                // أيقونة الجهاز
                GestureDetector(
                  onTap: tapOnIcon,
                  child: Container(
                    padding: EdgeInsets.all(controller.sized * 0.01),
                    decoration: BoxDecoration(
                      color: _getDeviceIconColor(type, connect),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: iconStyle(
                      icon: _getDeviceIcon(type),
                      color: AppColors.white,
                      size: controller.sized * 0.025,
                    ),
                  ),
                ),
                
                SizedBox(width: controller.sizedWidth * 0.03),
                
                // اسم الجهاز
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      txtStyle(
                        txt: PrivName,
                        color: AppColors.textPrimary,
                        size: controller.sized * 0.014,
                        align: TextAlign.start,
                      ),
                      SizedBox(height: controller.sizedHight * 0.002),
                      txtStyle(
                        txt: _getDeviceTypeText(type),
                        color: AppColors.textSecondary,
                        size: controller.sized * 0.011,
                        align: TextAlign.start,
                      ),
                    ],
                  ),
                ),
                
                // حالة الاتصال والمفتاح
                Column(
                  children: [
                    // مؤشر الاتصال
                    Container(
                      width: controller.sized * 0.012,
                      height: controller.sized * 0.012,
                      decoration: BoxDecoration(
                        color: connect ? AppColors.success : AppColors.error,
                        shape: BoxShape.circle,
                      ),
                    ),
                    
                    SizedBox(height: controller.sizedHight * 0.008),
                    
                    // مفتاح التشغيل
                    if (connect && deviceState != null)
                      switchStyle(
                        onChanged: switchState,
                        value: deviceState,
                        size: 0.8,
                      )
                    else
                      txtStyle(
                        txt: connect ? 'متصل' : 'غير متصل',
                        color: connect ? AppColors.success : AppColors.error,
                        size: controller.sized * 0.01,
                      ),
                  ],
                ),
              ],
            ),
          ),
          
          // المحتوى
          if (content.isNotEmpty) ...[
            Divider(
              color: AppColors.border,
              height: 1,
              indent: controller.sizedWidth * 0.04,
              endIndent: controller.sizedWidth * 0.04,
            ),
            
            Padding(
              padding: EdgeInsets.all(controller.sizedWidth * 0.04),
              child: Column(children: content),
            ),
          ],
        ],
      ),
    ),
  );
}

/// الحصول على أيقونة الجهاز حسب النوع
IconData _getDeviceIcon(String type) {
  switch (type.toLowerCase()) {
    case 'ac':
      return Icons.ac_unit;
    case 'tv':
      return Icons.tv;
    case 'sw':
      return Icons.lightbulb;
    case 'zain':
      return Icons.star;
    default:
      return Icons.device_hub;
  }
}

/// الحصول على لون أيقونة الجهاز
Color _getDeviceIconColor(String type, bool connect) {
  if (!connect) return AppColors.error;
  
  switch (type.toLowerCase()) {
    case 'ac':
      return Colors.blue;
    case 'tv':
      return Colors.purple;
    case 'sw':
      return Colors.orange;
    case 'zain':
      return AppColors.primary;
    default:
      return AppColors.textSecondary;
  }
}

/// الحصول على نص نوع الجهاز
String _getDeviceTypeText(String type) {
  switch (type.toLowerCase()) {
    case 'ac':
      return 'مكيف هواء';
    case 'tv':
      return 'تلفزيون';
    case 'sw':
      return 'مفتاح إضاءة';
    case 'zain':
      return 'جهاز زين';
    default:
      return 'جهاز';
  }
}
