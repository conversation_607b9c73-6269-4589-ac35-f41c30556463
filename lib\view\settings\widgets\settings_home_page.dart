import 'package:flutter/material.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/view/home/<USER>/settingPage.dart';

/// ويدجت صفحة الإعدادات الرئيسية
class SettingsHomePage extends StatelessWidget {
  final HomeController controller;
  final VoidCallback onRoutineWordsPressed;
  final VoidCallback onTasksPressed;
  final VoidCallback onAddRoomPressed;
  final VoidCallback onDevicesConnectPressed;
  final VoidCallback onAssetPressed;
  final VoidCallback onRollPressed;
  final VoidCallback onCameraPressed;
  final Function(String?) onEditNamesPressed;
  final Function(bool?, String?) onEditPrivNamePressed;

  const SettingsHomePage({
    Key? key,
    required this.controller,
    required this.onRoutineWordsPressed,
    required this.onTasksPressed,
    required this.onAddRoomPressed,
    required this.onDevicesConnectPressed,
    required this.onAssetPressed,
    required this.onRollPressed,
    required this.onCameraPressed,
    required this.onEditNamesPressed,
    required this.onEditPrivNamePressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return HomePage(
      roomPrivName: controller.home.value,
      image: controller.homeImage.value,
      homeType: controller.homeType.value,
      routinWords: onRoutineWordsPressed,
      tasks: onTasksPressed,
      addRoom: onAddRoomPressed,
      nunDevices: onDevicesConnectPressed,
      asset: onAssetPressed,
      roll: onRollPressed,
      camera: onCameraPressed,
      editNames: onEditNamesPressed,
      editPrivName: onEditPrivNamePressed,
      sizedWidth: controller.sizedWidth,
      sizedHeight: controller.sizedHight,
      sized: controller.sized,
    );
  }
}
