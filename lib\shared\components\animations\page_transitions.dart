import 'package:flutter/material.dart';

// حركة مخصصة للدخول إلى الغرف
class RoomEntranceRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  RoomEntranceRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 700),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // حركة انزلاق من اليمين
            var slideValue = Curves.easeInOutCubic.transform(animation.value);
            // حركة تكبير
            var scaleValue = Curves.elasticOut.transform(animation.value);
            // حركة شفافية
            var fadeValue = Curves.easeIn.transform(animation.value);

            return Transform.translate(
              offset: Offset(
                  (1 - slideValue) * MediaQuery.of(context).size.width, 0),
              child: Transform.scale(
                scale: 0.8 + (0.2 * scaleValue),
                child: Opacity(
                  opacity: fadeValue,
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(25 * (1 - slideValue)),
                      bottomLeft: Radius.circular(25 * (1 - slideValue)),
                    ),
                    child: child,
                  ),
                ),
              ),
            );
          },
        );
}

// حركة مخصصة أخرى للغرف - تأثير الباب المنزلق
class SlidingDoorRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlidingDoorRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 800),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // حركة انزلاق من اليمين
            var slideValue = Curves.easeInOutCubic.transform(animation.value);
            // حركة تكبير
            var scaleValue = Curves.elasticOut.transform(animation.value);
            // حركة شفافية
            var fadeValue = Curves.easeIn.transform(animation.value);

            return Transform.translate(
              offset: Offset(
                  (1 - slideValue) * MediaQuery.of(context).size.width, 0),
              child: Transform.scale(
                scale: 0.8 + (0.2 * scaleValue),
                child: Opacity(
                  opacity: fadeValue,
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(25 * (1 - slideValue)),
                      bottomLeft: Radius.circular(25 * (1 - slideValue)),
                    ),
                    child: child,
                  ),
                ),
              ),
            );
          },
        );
}

// حركة انزلاق من اليمين إلى اليسار
class SlideRightToLeftRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideRightToLeftRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 400),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
}

// حركة انزلاق من اليسار إلى اليمين
class SlideLeftToRightRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideLeftToRightRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 400),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(-1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOut;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            return SlideTransition(
              position: animation.drive(tween),
              child: child,
            );
          },
        );
}

// حركة انزلاق من الأسفل إلى الأعلى
class SlideBottomToTopRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideBottomToTopRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 500),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(0.0, 1.0);
            const end = Offset.zero;
            const curve = Curves.easeOutCubic;

            var tween = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            var fadeAnimation = Tween<double>(
              begin: 0.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeIn,
            ));

            return SlideTransition(
              position: animation.drive(tween),
              child: FadeTransition(
                opacity: fadeAnimation,
                child: child,
              ),
            );
          },
        );
}

// حركة تكبير مع شفافية
class ScaleRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  ScaleRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 600),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var scaleAnimation = Tween<double>(
              begin: 0.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.elasticOut,
            ));

            var fadeAnimation = Tween<double>(
              begin: 0.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeIn,
            ));

            return ScaleTransition(
              scale: scaleAnimation,
              child: FadeTransition(
                opacity: fadeAnimation,
                child: child,
              ),
            );
          },
        );
}

// حركة دوران مع تكبير
class RotationScaleRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  RotationScaleRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 800),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            var rotationAnimation = Tween<double>(
              begin: 0.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.elasticOut,
            ));

            var scaleAnimation = Tween<double>(
              begin: 0.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.elasticOut,
            ));

            return RotationTransition(
              turns: rotationAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: child,
              ),
            );
          },
        );
}

// حركة انزلاق مع تأثير العمق
class SlideWithDepthRoute extends PageRouteBuilder {
  final Widget page;
  final Duration duration;

  SlideWithDepthRoute({
    required this.page,
    this.duration = const Duration(milliseconds: 600),
  }) : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: duration,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0);
            const end = Offset.zero;
            const curve = Curves.easeInOutCubic;

            var slideAnimation = Tween(begin: begin, end: end).chain(
              CurveTween(curve: curve),
            );

            var scaleAnimation = Tween<double>(
              begin: 0.8,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: curve,
            ));

            var fadeAnimation = Tween<double>(
              begin: 0.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeIn,
            ));

            return SlideTransition(
              position: animation.drive(slideAnimation),
              child: ScaleTransition(
                scale: scaleAnimation,
                child: FadeTransition(
                  opacity: fadeAnimation,
                  child: child,
                ),
              ),
            );
          },
        );
}
