import 'package:flutter/material.dart';
import 'package:zaen/shared/commands/ac.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/components/constants.dart';
import '../../../../shared/components/config.dart';
import 'package:zaen/shared/themes/app_colors.dart';

Widget AC({
  device,
  room,
  required Function(bool?) switchState,
  required Function(double?) sliderState,
  required Function(int?) acTypeState,
  required Function() acSwingState,
  required Function() acSpeedsStateLeft,
  required Function() acSpeedsStateRight,
  required Function() acRun,
}) {
  return Builder(
    builder: (BuildContext context) {
      roomId = room;
      bool swingState = device['swing'];
      var speedState = device['speed'] - 1;
      var typeState = device['type'] == 'تدفئه'
          ? 0
          : device['type'] == 'تبريد'
              ? 1
              : 2;
      var degree = device['degree'];

      return Directionality(
        textDirection: TextDirection.rtl,
        child: Stack(children: [
          Container(
            height: controller.sizedHight * 0.33,
            padding:
                EdgeInsets.symmetric(horizontal: controller.sizedWidth * 0.04),
            child: Column(
              children: [
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.all(controller.sized * 0.01),
                        child: iconStyle(
                          icon: Icons.ac_unit_rounded,
                          color: AppColors.warningColor,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.zero,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.only(
                                      left: controller.sizedWidth * 0.001),
                                  decoration: BoxDecoration(
                                    border: Border(
                                      left: BorderSide(
                                        color: AppColors.textColor3
                                            .withOpacity(0.45),
                                        width: 1,
                                      ),
                                    ),
                                  ),
                                  child: txtStyle(
                                    align: TextAlign.right,
                                    txt: 'مكيف',
                                  ),
                                ),
                                Container(
                                  width: controller.sizedWidth * 0.35,
                                  padding: EdgeInsets.only(
                                      right: controller.sizedWidth * 0.01),
                                  child: txtStyle(
                                    align: TextAlign.right,
                                    txt: device['priv'] == 'x'
                                        ? 'لا يوجد اسم'
                                        : device['priv'].split('_')[0].length >
                                                14
                                            ? device['priv']
                                                    .split('_')[0]
                                                    .substring(0, 13) +
                                                '...'
                                            : device['priv'].split('_')[0],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: controller.sizedHight * 0.005),
                            Row(
                              children: [
                                txtStyle(
                                    txt: controller.rooms[room]['privName']),
                              ],
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(controller.sized * 0.008),
                        child: switchStyle(
                          value: device['state'] ?? false,
                          onChanged: (val) {
                            roomId = room;
                            commandAc(
                              val!,
                              controller.rooms[room]['devices'][device['id']],
                              room,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                // Temperature Control
                Container(
                  padding: EdgeInsets.only(
                      left: controller.sizedWidth * 0.04,
                      right: controller.sizedWidth * 0.04,
                      top: controller.sizedHight * 0.005,
                      bottom: controller.sizedHight * 0.01),
                  decoration: BoxDecoration(
                    color: AppColors.surface.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                        color: AppColors.border.withOpacity(0.2), width: 1),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadow.withOpacity(0.08),
                        blurRadius: 12,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Icon(Icons.thermostat_rounded,
                              color: AppColors.primary,
                              size: controller.sized * 0.02),
                          SizedBox(width: controller.sizedWidth * 0.02),
                          Expanded(
                            child: Text(
                              'درجة الحرارة',
                              style: TextStyle(
                                color: AppColors.textSecondary,
                                fontSize: controller.sized * 0.012,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: controller.sizedWidth * 0.006,
                              vertical: controller.sizedHight * 0.001,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.primary.withOpacity(0.1),
                                  AppColors.primary.withOpacity(0.05)
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                  color: AppColors.primary.withOpacity(0.2),
                                  width: 1.5),
                            ),
                            child: Text(
                              '${degree.toInt()}°C',
                              style: TextStyle(
                                fontSize: controller.sized * 0.012,
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 1.2,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: controller.sizedHight * 0.006,
                      ),
                      Container(
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('16°',
                                    style: TextStyle(
                                        color: AppColors.textHint,
                                        fontSize: controller.sized * 0.01,
                                        fontWeight: FontWeight.w500)),
                                Text('30°',
                                    style: TextStyle(
                                        color: AppColors.textHint,
                                        fontSize: controller.sized * 0.01,
                                        fontWeight: FontWeight.w500)),
                              ],
                            ),
                            SliderTheme(
                              data: SliderTheme.of(context).copyWith(
                                activeTrackColor: AppColors.primary,
                                inactiveTrackColor:
                                    AppColors.border.withOpacity(0.3),
                                thumbColor: AppColors.primary,
                                thumbShape: RoundSliderThumbShape(
                                    enabledThumbRadius:
                                        controller.sized * 0.005),
                                overlayColor:
                                    AppColors.primary.withOpacity(0.2),
                                overlayShape: RoundSliderOverlayShape(
                                    overlayRadius: controller.sized * 0.001),
                                trackHeight: controller.sized * 0.002,
                                valueIndicatorColor: AppColors.primary,
                                valueIndicatorTextStyle: TextStyle(
                                  color: AppColors.white,
                                  fontSize: controller.sized * 0.012,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              child: Slider(
                                min: 16,
                                max: 30,
                                divisions: 14,
                                value: degree.toDouble(),
                                onChanged: sliderState,
                                label: '${degree.toInt()}°C',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: controller.sizedHight * 0.01),
                // Fan Speed Control with Swing - Temperature Style
                Container(
                  padding: EdgeInsets.only(
                      left: controller.sizedWidth * 0.04,
                      right: controller.sizedWidth * 0.04,
                      top: controller.sizedHight * 0.005,
                      bottom: controller.sizedHight * 0.01),
                  decoration: BoxDecoration(
                    color: AppColors.surface.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                        color: AppColors.border.withOpacity(0.2), width: 1),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.shadow.withOpacity(0.08),
                        blurRadius: 12,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Fan Speed Controls with Header and Swing Icon
                      Row(
                        children: [
                          // Fan Speed Header
                          Icon(Icons.air_rounded,
                              color: AppColors.primary,
                              size: controller.sized * 0.02),
                          SizedBox(width: controller.sizedWidth * 0.015),
                          Text(
                            'المروحة',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: controller.sized * 0.012,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: controller.sizedWidth * 0.02),

                          // Fan Speed Controls
                          Expanded(
                            flex: 3,
                            child: Container(
                              height: controller.sizedHight * 0.04,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                    colors: [
                                      AppColors.surfaceElevated,
                                      AppColors.surface
                                    ],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                    color: AppColors.border.withOpacity(0.3),
                                    width: 1),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: acSpeedsStateLeft,
                                      child: Container(
                                        child: Icon(
                                          Icons.remove_rounded,
                                          color: speedState > 0
                                              ? AppColors.primary
                                              : AppColors.textHint,
                                          size: controller.sized * 0.015,
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    flex: 2,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        color:
                                            AppColors.primary.withOpacity(0.1),
                                        border: Border.symmetric(
                                          vertical: BorderSide(
                                              color: AppColors.border
                                                  .withOpacity(0.3),
                                              width: 1),
                                        ),
                                      ),
                                      child: Center(
                                        child: Text(
                                          acFanSpeed[speedState],
                                          style: TextStyle(
                                            fontSize: controller.sized * 0.01,
                                            color: AppColors.primary,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: acSpeedsStateRight,
                                      child: Container(
                                        child: Icon(
                                          Icons.add_rounded,
                                          color: speedState < 3
                                              ? AppColors.primary
                                              : AppColors.textHint,
                                          size: controller.sized * 0.015,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          SizedBox(width: controller.sizedWidth * 0.02),

                          // Swing Icon Button
                          GestureDetector(
                            onTap: acSwingState,
                            child: Container(
                              width: controller.sizedWidth * 0.1,
                              height: controller.sizedHight * 0.04,
                              decoration: BoxDecoration(
                                gradient: swingState
                                    ? LinearGradient(
                                        colors: [
                                          AppColors.success,
                                          AppColors.success.withOpacity(0.8),
                                        ],
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                      )
                                    : LinearGradient(
                                        colors: [
                                          AppColors.surfaceElevated,
                                          AppColors.surface,
                                        ],
                                        begin: Alignment.topCenter,
                                        end: Alignment.bottomCenter,
                                      ),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: swingState
                                      ? AppColors.success.withOpacity(0.3)
                                      : AppColors.border.withOpacity(0.3),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: swingState
                                        ? AppColors.success.withOpacity(0.2)
                                        : AppColors.shadow.withOpacity(0.05),
                                    blurRadius: 6,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.swap_vert_rounded,
                                color: swingState
                                    ? AppColors.white
                                    : AppColors.textSecondary,
                                size: controller.sized * 0.018,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: controller.sizedHight * 0.01),
                // Mode Control Buttons - Moved here from above
                Row(
                  children: [
                    Container(
                      height: controller.sizedHight * 0.07,
                      width: controller.sizedWidth * 0.6,
                      decoration: BoxDecoration(
                        color: AppColors.surface.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                            color: AppColors.border.withOpacity(0.3), width: 1),
                      ),
                      child: Row(
                        children: [
                          _buildModeButton(
                            context: context,
                            label: 'تدفئة',
                            icon: Icons.local_fire_department_rounded,
                            isSelected: typeState == 0,
                            onTap: () => acTypeState(0),
                            color: Colors.orange,
                          ),
                          _buildModeButton(
                            context: context,
                            label: 'تبريد',
                            icon: Icons.ac_unit_rounded,
                            isSelected: typeState == 1,
                            onTap: () => acTypeState(1),
                            color: Colors.blue,
                          ),
                          _buildModeButton(
                            context: context,
                            label: 'مروحة',
                            icon: Icons.air_rounded,
                            isSelected: typeState == 2,
                            onTap: () => acTypeState(2),
                            color: Colors.green,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Green Play Button - Positioned at left-bottom (Rotated 180°)
          Positioned(
            right: controller.sizedWidth * 0.65,
            top: controller.sizedHight * 0.23,
            child: IconButton(
              onPressed: acRun,
              icon: Transform.rotate(
                angle: 3.14159, // 180 degrees in radians (π)
                child: Icon(
                  Icons.play_circle_rounded,
                  color: AppColors.success,
                  size: controller.sized * 0.065,
                ),
              ),
            ),
          ),
        ]),
      );
    },
  );
}

// Helper function to build mode toggle buttons
Widget _buildModeButton({
  required BuildContext context,
  required String label,
  required IconData icon,
  required bool isSelected,
  required VoidCallback onTap,
  required Color color,
}) {
  return Expanded(
    child: GestureDetector(
      onTap: onTap,
      child: Container(
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  colors: [
                    color,
                    color.withOpacity(0.8),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                )
              : null,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.white : AppColors.textHint,
              size: controller.sized * 0.015,
            ),
            SizedBox(height: controller.sizedHight * 0.003),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? AppColors.white : AppColors.textHint,
                fontSize: controller.sized * 0.009,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
