import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/themes/app_colors.dart';

/// ويدجت قائمة الأنظمة في الصفحة الرئيسية
class HomeMenu extends StatelessWidget {
  final HomeController controller;
  final Function(String) onSelected;

  const HomeMenu({
    Key? key,
    required this.controller,
    required this.onSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Icon(
        Icons.more_vert,
        color: AppColors.textPrimary,
        size: controller.sized * 0.025,
      ),
      color: AppColors.backgroundColor2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
        side: BorderSide(
          color: AppColors.border,
          width: 1.0,
        ),
      ),
      itemBuilder: (BuildContext context) {
        List<PopupMenuEntry<String>> menuItems = [];

        // إضافة خيار "نظام جديد"
        menuItems.add(
          PopupMenuItem<String>(
            value: 'new',
            child: Row(
              children: [
                Icon(
                  Icons.add_circle_outline,
                  color: AppColors.primary,
                  size: controller.sized * 0.02,
                ),
                SizedBox(width: controller.sizedWidth * 0.03),
                txtStyle(
                  txt: 'نظام جديد',
                  color: AppColors.textPrimary,
                  size: controller.sized * 0.013,
                  align: TextAlign.right,
                ),
              ],
            ),
          ),
        );

        // إضافة فاصل إذا كان هناك أنظمة
        if (controller.systems.isNotEmpty) {
          menuItems.add(
            PopupMenuItem<String>(
              enabled: false,
              height: 0,
              padding: EdgeInsets.zero,
              child: Divider(
                color: AppColors.textColor.withOpacity(0.2),
              ),
            ),
          );
        }

        // إضافة الأنظمة الموجودة
        for (int i = 0; i < controller.systems.length; i++) {
          var system = controller.systems[i];
          
          menuItems.add(
            PopupMenuItem<String>(
              value: system['id'].toString(),
              child: Row(
                children: [
                  // أيقونة النظام
                  Container(
                    padding: EdgeInsets.all(controller.sized * 0.005),
                    decoration: BoxDecoration(
                      color: _getSystemColor(system['type']).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getSystemIcon(system['type']),
                      color: _getSystemColor(system['type']),
                      size: controller.sized * 0.018,
                    ),
                  ),
                  
                  SizedBox(width: controller.sizedWidth * 0.03),
                  
                  // معلومات النظام
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        txtStyle(
                          txt: system['name'] ?? 'نظام غير معروف',
                          color: AppColors.textPrimary,
                          size: controller.sized * 0.013,
                          align: TextAlign.start,
                        ),
                        SizedBox(height: controller.sizedHight * 0.002),
                        txtStyle(
                          txt: _getSystemTypeText(system['type']),
                          color: AppColors.textSecondary,
                          size: controller.sized * 0.011,
                          align: TextAlign.start,
                        ),
                      ],
                    ),
                  ),
                  
                  // مؤشر الحالة
                  Container(
                    width: controller.sized * 0.01,
                    height: controller.sized * 0.01,
                    decoration: BoxDecoration(
                      color: system['connected'] == true 
                          ? AppColors.success 
                          : AppColors.error,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
            ),
          );

          // إضافة فاصل إذا لم يكن العنصر الأخير
          if (i < controller.systems.length - 1) {
            menuItems.add(
              PopupMenuItem<String>(
                enabled: false,
                height: 0,
                padding: EdgeInsets.zero,
                child: Divider(
                  color: AppColors.textColor.withOpacity(0.2),
                ),
              ),
            );
          }
        }

        return menuItems;
      },
      onSelected: onSelected,
      constraints: BoxConstraints(
        maxHeight: controller.sizedHight * 0.3,
        minWidth: controller.sizedWidth * 0.6,
        maxWidth: controller.sizedWidth * 0.6,
      ),
    );
  }

  /// الحصول على أيقونة النظام حسب النوع
  IconData _getSystemIcon(String? type) {
    switch (type?.toLowerCase()) {
      case 'home':
        return Icons.home;
      case 'security':
        return Icons.security;
      case 'lighting':
        return Icons.lightbulb;
      case 'climate':
        return Icons.thermostat;
      default:
        return Icons.device_hub;
    }
  }

  /// الحصول على لون النظام حسب النوع
  Color _getSystemColor(String? type) {
    switch (type?.toLowerCase()) {
      case 'home':
        return AppColors.primary;
      case 'security':
        return Colors.red;
      case 'lighting':
        return Colors.orange;
      case 'climate':
        return Colors.blue;
      default:
        return AppColors.textSecondary;
    }
  }

  /// الحصول على نص نوع النظام
  String _getSystemTypeText(String? type) {
    switch (type?.toLowerCase()) {
      case 'home':
        return 'نظام المنزل';
      case 'security':
        return 'نظام الأمان';
      case 'lighting':
        return 'نظام الإضاءة';
      case 'climate':
        return 'نظام التكييف';
      default:
        return 'نظام';
    }
  }
}
