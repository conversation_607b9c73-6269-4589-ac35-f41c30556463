import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/shared/settings/settings.dart';
import 'package:get/get.dart';
import '../../../controller/controller.dart';

// متغير controller مشترك
final HomeController controller = HomeController();

/// مفتاح التبديل العادي
Widget switchStyle(
    {required Function(bool?) onChanged,
    required bool value,
    double size = 0}) {
  if (size == 0) {
    size = controller.sized * 0.0008;
  }
  return Transform.scale(
    scale: size,
    child: CupertinoSwitch(
      value: value,
      onChanged: onChanged,
      activeColor: AppColors.primary,
      trackColor: AppColors.switchTrack,
      thumbColor: AppColors.switchThumb,
    ),
  );
}

/// مفتاح مخصص مع أيقونة داخلية للثيم - نفس تصميم المفتاح العادي
Widget themeSwitchStyle({
  required Function(bool?) onChanged,
  required bool value,
  double size = 0,
}) {
  if (size == 0) {
    size = controller.sized * 0.001;
  }

  return GetBuilder<SettingsController>(
    builder: (settingsController) => Transform.scale(
      scale: size,
      child: Container(
        width: 60,
        height: 32,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: value ? AppColors.primary : AppColors.switchTrack,
        ),
        child: Stack(
          children: [
            AnimatedPositioned(
              duration: const Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              left: value ? 28 : 4,
              top: 4,
              child: GestureDetector(
                onTap: () => onChanged(!value),
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.switchThumb,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    value ? Icons.dark_mode : Icons.light_mode,
                    size: 16,
                    color: value ? AppColors.primary : Colors.orange,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
