import 'dart:convert';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:sqflite/sqflite.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import '../widgets/text_and_icons.dart';
import '../widgets/text_fields.dart';
import '../widgets/containers.dart';

// متغير controller مشترك
final HomeController controller = HomeController();

/// ويدجت إعدادات الصفحة
Widget pageSetting({
  String? roomN,
  bool connect = true,
  String? privName,
  bool isTv = false,
  Function()? tapOn_star,
  Function()? tapOn_edit,
  Function()? tapOn_del,
  Function()? tapOn_fav,
  Function()? tapOn_connect,
  Function()? tapOn_disconnect,
  Function()? tapOn_image,
  Function()? tapOn_name,
  Function()? tapOn_room,
  Function()? tapOn_type,
  Function()? tapOn_save,
  Function()? tapOn_cancel,
  TextEditingController? nameController,
  TextEditingController? roomController,
  String? selectedType,
  String? selectedRoom,
  String? imagePath,
}) {
  return GetBuilder<HomeController>(
    builder: (controller) => Container(
      height: double.infinity,
      width: double.infinity,
      margin: EdgeInsets.only(top: controller.sizedHight * 0.1),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor2.withOpacity(0.989),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(25.5)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          // مؤشر السحب
          Container(
            margin: EdgeInsets.symmetric(vertical: controller.sizedHight * 0.01),
            height: controller.sizedHight * 0.005,
            width: controller.sizedWidth * 0.1,
            decoration: BoxDecoration(
              color: AppColors.textSecondary.withOpacity(0.3),
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          
          // العنوان
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: controller.sizedWidth * 0.05,
              vertical: controller.sizedHight * 0.02,
            ),
            child: txtStyle(
              txt: roomN ?? 'إعدادات الجهاز',
              size: controller.sized * 0.018,
              color: AppColors.textPrimary,
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: controller.sizedWidth * 0.05,
              ),
              child: Column(
                children: [
                  // حقل الاسم
                  if (nameController != null) ...[
                    customTextFormField(
                      textController: nameController,
                      hintText: 'اسم الجهاز',
                      labelText: 'الاسم',
                      prefixIcon: Icons.device_hub,
                    ),
                    SizedBox(height: controller.sizedHight * 0.015),
                  ],

                  // حقل الغرفة
                  if (roomController != null) ...[
                    customTextFormField(
                      textController: roomController,
                      hintText: 'اسم الغرفة',
                      labelText: 'الغرفة',
                      prefixIcon: Icons.room,
                    ),
                    SizedBox(height: controller.sizedHight * 0.015),
                  ],

                  // خيارات التلفزيون
                  if (isTv) ...[
                    containerPageOption(
                      content: Column(
                        children: [
                          MaterialButton(
                            padding: EdgeInsets.zero,
                            onPressed: () {
                              tapOn_star?.call();
                            },
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                iconStyle(icon: Icons.menu_open_rounded),
                                Expanded(child: SizedBox(width: double.infinity)),
                                txtStyle(
                                  align: TextAlign.right,
                                  txt: 'اسماء القنوات المفضله',
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: controller.sizedHight * 0.015),
                  ],

                  // خيارات الاتصال
                  containerPageOption(
                    content: Column(
                      children: [
                        if (connect) ...[
                          // زر قطع الاتصال
                          MaterialButton(
                            padding: EdgeInsets.zero,
                            onPressed: tapOn_disconnect,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                iconStyle(
                                  icon: Icons.link_off,
                                  color: AppColors.error,
                                ),
                                Expanded(child: SizedBox(width: double.infinity)),
                                txtStyle(
                                  align: TextAlign.right,
                                  txt: 'قطع الاتصال',
                                  color: AppColors.error,
                                ),
                              ],
                            ),
                          ),
                        ] else ...[
                          // زر الاتصال
                          MaterialButton(
                            padding: EdgeInsets.zero,
                            onPressed: tapOn_connect,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                iconStyle(
                                  icon: Icons.link,
                                  color: AppColors.success,
                                ),
                                Expanded(child: SizedBox(width: double.infinity)),
                                txtStyle(
                                  align: TextAlign.right,
                                  txt: 'اتصال',
                                  color: AppColors.success,
                                ),
                              ],
                            ),
                          ),
                        ],

                        Divider(color: AppColors.border),

                        // زر التعديل
                        MaterialButton(
                          padding: EdgeInsets.zero,
                          onPressed: tapOn_edit,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              iconStyle(icon: Icons.edit),
                              Expanded(child: SizedBox(width: double.infinity)),
                              txtStyle(
                                align: TextAlign.right,
                                txt: 'تعديل',
                              ),
                            ],
                          ),
                        ),

                        Divider(color: AppColors.border),

                        // زر الحذف
                        MaterialButton(
                          padding: EdgeInsets.zero,
                          onPressed: tapOn_del,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              iconStyle(
                                icon: Icons.delete,
                                color: AppColors.error,
                              ),
                              Expanded(child: SizedBox(width: double.infinity)),
                              txtStyle(
                                align: TextAlign.right,
                                txt: 'حذف',
                                color: AppColors.error,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: controller.sizedHight * 0.02),

                  // أزرار الحفظ والإلغاء
                  Row(
                    children: [
                      Expanded(
                        child: MaterialButton(
                          onPressed: tapOn_cancel,
                          child: txtStyle(
                            txt: 'إلغاء',
                            color: AppColors.textSecondary,
                          ),
                          color: AppColors.backgroundColor3,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                      SizedBox(width: controller.sizedWidth * 0.03),
                      Expanded(
                        child: MaterialButton(
                          onPressed: tapOn_save,
                          child: txtStyle(
                            txt: 'حفظ',
                            color: AppColors.white,
                          ),
                          color: AppColors.primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: controller.sizedHight * 0.03),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}
