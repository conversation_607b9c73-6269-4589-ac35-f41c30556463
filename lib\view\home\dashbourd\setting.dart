// ملف setting.dart الرئيسي - يحتوي على export للملف المقسم
export '../../settings/settings_main.dart';
  return GetBuilder<HomeController>(
      builder: (controller) => HomePage(
          roomPrivName: controller.home.value,
          image: controller.homeImage.value,
          homeType: controller.homeType.value,
          routinWords: () {
            routineWords(context, setState);
          },
          tasks: () {
            tasks(context, setState);
          },
          addRoom: () {
            addRoom(context);
          },
          nunDevices: () {
            devicesConnect(context, setState);
          },
          asset: () async {
            final manifestContent =
                await rootBundle.loadString('AssetManifest.json');

            Map<String, dynamic> manifestMap =
                await json.decode(manifestContent);
            // >> To get paths you need these 2 lines

            List imagePaths = await manifestMap.keys
                .where((String key) =>
                    key.contains('images/places/${controller.homeType.value}/'))
                .toList();
            print(imagePaths);
            var item = imagePaths.indexOf(controller.homeImage.value);
            if (item == -1) {
              item = imagePaths.length;
            }
            print(item);

            await showCupertinoModalPopup(
                context: context,
                barrierColor: AppColors.textColor2.withOpacity(0.35),
                builder: (builder) {
                  return StatefulBuilder(
                      builder: ((context, setState) => Center(
                            child: Container(
                                width: controller.sizedWidth,
                                height: controller.sizedHight * 0.3,
                                decoration: BoxDecoration(
                                  color: AppColors.backgroundColor2
                                      .withOpacity(0.975),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: RotatedBox(
                                        quarterTurns: 1,
                                        child: Center(
                                          child: ListWheelScrollView(
                                              magnification: 5,
                                              // useMagnifier: true,
                                              controller:
                                                  FixedExtentScrollController(
                                                      initialItem: item),
                                              // magnification: 2.0,

                                              itemExtent:
                                                  controller.sizedWidth * 0.55,
                                              diameterRatio: 20,
                                              squeeze: 0.88,
                                              // overAndUnderCenterOpacity: 0.5,
                                              // scrollDirection:
                                              //     Axis.horizontal,
                                              // reverse: true,
                                              onSelectedItemChanged: (x) {
                                                print(x);
                                                setState(() {
                                                  item = x;
                                                });
                                              },
                                              children: List.generate(
                                                imagePaths.length,
                                                (index) => RotatedBox(
                                                  quarterTurns: -1,
                                                  child: Center(
                                                    child: AnimatedContainer(
                                                      duration: Duration(
                                                          milliseconds: 450),
                                                      width: item == index
                                                          ? controller
                                                                  .sizedWidth *
                                                              0.7
                                                          : controller
                                                                  .sizedWidth *
                                                              0.5,
                                                      height: item == index
                                                          ? controller
                                                                  .sizedHight *
                                                              0.16
                                                          : controller
                                                                  .sizedHight *
                                                              0.13,
                                                      decoration: BoxDecoration(
                                                          image:
                                                              DecorationImage(
                                                        image: AssetImage(
                                                            imagePaths[index]
                                                                .toString()),
                                                        onError: (context,
                                                            stackTrace) {},
                                                        colorFilter:
                                                            ColorFilter.mode(
                                                          item == index
                                                              ? AppColors
                                                                  .textColor
                                                                  .withOpacity(
                                                                      0.1)
                                                              : AppColors
                                                                  .subtitleColor
                                                                  .withOpacity(
                                                                      0.6),
                                                          BlendMode.darken,
                                                        ),
                                                        fit: BoxFit.cover,
                                                        filterQuality:
                                                            FilterQuality.high,
                                                      )),
                                                    ),
                                                  ),
                                                ),
                                              )),
                                        ),
                                      ),
                                    ),
                                    submitButtom(
                                      onPressed: () async {
                                        controller.homeImage.value =
                                            imagePaths[item];
                                        var appDB = await openDatabase(
                                            '${controller.system}.db',
                                            version: 3);
                                        await appDB.rawQuery(
                                            "UPDATE home set image='${imagePaths[item]}'");
                                        controller.homeImage.value =
                                            imagePaths[item];
                                        controller.update();
                                      },
                                    ),
                                    SizedBox(
                                      height: controller.sizedHight * 0.01,
                                    )
                                  ],
                                )),
                          )));
                });
          },
          roll: () async {
            XFile? image =
                await ImagePicker().pickImage(source: ImageSource.gallery);
            if (image == null) {
              print('555555555555555555555555');
              return;
            }
            final imageTemp = File(image.path);
            print(imageTemp.path);
            controller.homeImage.value = imageTemp.path;
            var appDB =
                await openDatabase('${controller.system}.db', version: 3);
            await appDB.rawQuery('UPDATE home set image="${imageTemp.path}"');
            controller.update();
          },
          camera: () async {
            XFile? image =
                await ImagePicker().pickImage(source: ImageSource.camera);
            if (image == null) {
              print('555555555555555555555555');
              return;
            }
            final imageTemp = File(image.path);
            print(imageTemp.path);
            controller.homeImage.value = imageTemp.path;
            var appDB =
                await openDatabase('${controller.system}.db', version: 3);
            await appDB.rawQuery('UPDATE home set image="${imageTemp.path}"');
            controller.update();
          },
          del: () {},
          editNames: (string) {},
          editPrivName: (privN, priv) {
            print(privN!);
            if (privN) {
              print(123);
              void s() async {
                var appDB =
                    await openDatabase('${controller.system}.db', version: 3);
                print(1234);
                var myHome = await appDB.rawQuery('SELECT * FROM home');

                print(myHome);

                await appDB.transaction((txn) async {
                  print(12345);
                  await txn.rawUpdate('UPDATE home SET name = ?', [
                    priv,
                  ]);
                  print(123456);
                });

                myHome = await appDB.rawQuery('SELECT * FROM home');
                print(myHome);
                await appDB.close();
              }

              s();
              controller.home.value = priv!;
              // i['priv']=priv;
              privN = false;
            } else {
              priv =
                  controller.home.value != null ? controller.home.value : 'X';
            }
            controller.home.value = priv;
            controller.update();
          },
          sizedWidth: controller.sizedWidth,
          sizedHeight: controller.sizedHight,
          sized: controller.sized));
}
