import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/material.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/view/home/<USER>/routine.dart';
import 'package:zaen/view/room/double_tap/del.dart';
import 'package:zaen/shared/themes/app_colors.dart';

devicesConnect(context, setState) async {
  if (client.connectionStatus!.state.name == 'connected') {
    final conn = await MySqlConnection.connect(ConnectionSettings(
        host: controller.hostZain.value,
        // port: 80,
        user: 'root',
        db: 'zain',
        password: 'zain',
        characterSet: CharacterSet.UTF8));

    var appDB = await openDatabase('${controller.system}.db', version: 3);

    Map<String, String> rooms = {};
    Map devices = {};
    Map<String, String> rooms1 = {};
    Results appdevices = await conn.query("SELECT id,Type,rooms FROM Devices");
    print(appdevices);

    for (var r = 0; r < controller.rooms.keys.toList().length; r++) {
      rooms[controller.rooms[controller.rooms.keys.toList()[r]]['id']] =
          controller.rooms[controller.rooms.keys.toList()[r]]['privName'];
      for (var d in controller
          .rooms[controller.rooms.keys.toList()[r]]['devices'].values) {
        devices[d['id']] =
            controller.rooms[controller.rooms.keys.toList()[r]]['id'];
      }
    }
    for (var r in appdevices) {
      if (r.fields['rooms'] == 'x') {
        devices[r.fields['id']] = 'x';
      }
    }

    rooms1['x'] = 'بدون غرفة';
    rooms1.addAll(rooms);
    print(devices);
    print('1111111111111111111111111111111111111');

    // فصل الأجهزة حسب النوع
    List<dynamic> unassignedDevices =
        appdevices.where((device) => device.fields['rooms'] == 'x').toList();
    List<dynamic> assignedDevices =
        appdevices.where((device) => device.fields['rooms'] != 'x').toList();

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: EdgeInsets.symmetric(
            horizontal: controller.sized * 0.02,
            vertical: controller.sized * 0.05,
          ),
          child: Container(
            width: double.infinity,
            height: controller.sizedHight * 0.8,
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              borderRadius: BorderRadius.circular(controller.sized * 0.015),
            ),
            child: StatefulBuilder(builder: (context, setDialogState) {
              return Column(
                children: [
                  // Header
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: controller.sized * 0.02,
                      vertical: controller.sized * 0.015,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(controller.sized * 0.015),
                        topRight: Radius.circular(controller.sized * 0.015),
                      ),
                      border: Border(
                        bottom: BorderSide(
                          color: AppColors.textColor.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                'إدارة الأجهزة',
                                style: TextStyle(
                                  color: AppColors.textColor,
                                  fontSize: controller.sized * 0.018,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: controller.sized * 0.003),
                              Text(
                                'تنظيم وإدارة أجهزة المنزل الذكي',
                                style: TextStyle(
                                  color: AppColors.textColor.withOpacity(0.6),
                                  fontSize: controller.sized * 0.011,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: controller.sized * 0.01,
                            vertical: controller.sized * 0.005,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor.withOpacity(0.1),
                            borderRadius:
                                BorderRadius.circular(controller.sized * 0.015),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.devices,
                                color: AppColors.primaryColor,
                                size: controller.sized * 0.012,
                              ),
                              SizedBox(width: controller.sized * 0.005),
                              Text(
                                '${appdevices.length}',
                                style: TextStyle(
                                  color: AppColors.primaryColor,
                                  fontSize: controller.sized * 0.012,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.all(controller.sized * 0.015),
                      child: Column(
                        children: [
                          // قسم الأجهزة غير المخصصة (يظهر فقط إذا كان هناك أجهزة)
                          if (unassignedDevices.isNotEmpty) ...[
                            _buildDialogSectionHeader(
                              'الأجهزة الجديدة',
                              'أجهزة تحتاج إلى تخصيص غرفة',
                              Icons.fiber_new,
                              unassignedDevices.length,
                            ),
                            SizedBox(height: controller.sized * 0.01),
                            ...unassignedDevices.map((device) =>
                                _buildDialogUnassignedCard(device, rooms1,
                                    devices, conn, setDialogState, setState)),
                            SizedBox(height: controller.sized * 0.02),
                          ],

                          // قسم الأجهزة المخصصة
                          _buildDialogSectionHeader(
                            'الأجهزة المخصصة',
                            'أجهزة مرتبطة بالغرف',
                            Icons.home_outlined,
                            assignedDevices.length,
                          ),
                          SizedBox(height: controller.sized * 0.01),

                          if (assignedDevices.isEmpty)
                            _buildDialogEmptyState()
                          else
                            ...assignedDevices.map((device) =>
                                _buildDialogAssignedCard(
                                    device,
                                    rooms,
                                    devices,
                                    conn,
                                    appDB,
                                    setDialogState,
                                    setState,
                                    context)),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            }),
          ),
        );
      },
    );
  }
}

// دالة لبناء عنوان القسم للحوار
Widget _buildDialogSectionHeader(
    String title, String subtitle, IconData icon, int count) {
  return Container(
    padding: EdgeInsets.all(controller.sized * 0.012),
    margin: EdgeInsets.only(bottom: controller.sized * 0.008),
    decoration: BoxDecoration(
      color: AppColors.backgroundColor2.withOpacity(0.5),
      borderRadius: BorderRadius.circular(controller.sized * 0.008),
      border: Border.all(
        color: AppColors.textColor.withOpacity(0.1),
        width: 1,
      ),
    ),
    child: Row(
      children: [
        Icon(
          icon,
          color: AppColors.primaryColor,
          size: controller.sized * 0.016,
        ),
        SizedBox(width: controller.sized * 0.01),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: AppColors.textColor,
                  fontSize: controller.sized * 0.014,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(
                  color: AppColors.textColor.withOpacity(0.6),
                  fontSize: controller.sized * 0.01,
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: controller.sized * 0.008,
            vertical: controller.sized * 0.003,
          ),
          decoration: BoxDecoration(
            color: AppColors.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(controller.sized * 0.01),
          ),
          child: Text(
            '$count',
            style: TextStyle(
              color: AppColors.primaryColor,
              fontSize: controller.sized * 0.011,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    ),
  );
}

// دالة لبناء حالة فارغة للحوار
Widget _buildDialogEmptyState() {
  return Container(
    padding: EdgeInsets.all(controller.sized * 0.025),
    decoration: BoxDecoration(
      color: AppColors.backgroundColor2.withOpacity(0.3),
      borderRadius: BorderRadius.circular(controller.sized * 0.008),
      border: Border.all(
        color: AppColors.textColor.withOpacity(0.1),
        width: 1,
      ),
    ),
    child: Column(
      children: [
        Icon(
          Icons.devices_other,
          size: controller.sized * 0.03,
          color: AppColors.textColor.withOpacity(0.4),
        ),
        SizedBox(height: controller.sized * 0.01),
        Text(
          'لا توجد أجهزة مخصصة',
          style: TextStyle(
            color: AppColors.textColor,
            fontSize: controller.sized * 0.013,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          'جميع الأجهزة بحاجة إلى تخصيص غرفة',
          style: TextStyle(
            color: AppColors.textColor.withOpacity(0.6),
            fontSize: controller.sized * 0.01,
          ),
        ),
      ],
    ),
  );
}

// دالة للحصول على اسم نوع الجهاز
String _getDeviceTypeName(String type) {
  switch (type) {
    case 'AC':
      return 'مكيف';
    case 'TV':
      return 'تلفاز';
    case 'ZAIN-Main':
      return 'مساعد صوتي - رئيسي';
    case 'ZAIN':
      return 'مساعد صوتي';
    default:
      return type.contains('SWITCH') ? 'مفاتيح' : type;
  }
}

// دالة للحصول على أيقونة نوع الجهاز
IconData _getDeviceIcon(String type) {
  switch (type) {
    case 'AC':
      return Icons.ac_unit;
    case 'TV':
      return Icons.tv;
    case 'ZAIN-Main':
      return Icons.mic;
    case 'ZAIN':
      return Icons.speaker;
    default:
      return type.contains('SWITCH')
          ? Icons.electrical_services
          : Icons.device_unknown;
  }
}

// دالة للحصول على لون نوع الجهاز
Color _getDeviceColor(String type) {
  switch (type) {
    case 'AC':
      return Colors.blue;
    case 'TV':
      return Colors.purple;
    case 'ZAIN-Main':
      return AppColors.primaryColor;
    case 'ZAIN':
      return AppColors.warningColor;
    default:
      return type.contains('SWITCH') ? Colors.green : Colors.grey;
  }
}

// دالة لبناء بطاقة جهاز غير مخصص للحوار
Widget _buildDialogUnassignedCard(
  dynamic device,
  Map<String, String> rooms1,
  Map devices,
  MySqlConnection conn,
  Function setDialogState,
  Function setState,
) {
  String deviceType = device.fields['Type'];
  String deviceId = device.fields['id'];

  return Container(
    margin: EdgeInsets.only(bottom: controller.sized * 0.008),
    padding: EdgeInsets.all(controller.sized * 0.012),
    decoration: BoxDecoration(
      color: AppColors.backgroundColor2.withOpacity(0.3),
      borderRadius: BorderRadius.circular(controller.sized * 0.008),
      border: Border.all(
        color: AppColors.primaryColor.withOpacity(0.2),
        width: 1,
      ),
    ),
    child: Column(
      children: [
        // Header
        Row(
          children: [
            Icon(
              _getDeviceIcon(deviceType),
              color: AppColors.textColor.withOpacity(0.7),
              size: controller.sized * 0.018,
            ),
            SizedBox(width: controller.sized * 0.01),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        _getDeviceTypeName(deviceType),
                        style: TextStyle(
                          color: AppColors.textColor,
                          fontSize: controller.sized * 0.013,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: controller.sized * 0.006),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: controller.sized * 0.006,
                          vertical: controller.sized * 0.002,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.primaryColor,
                          borderRadius:
                              BorderRadius.circular(controller.sized * 0.008),
                        ),
                        child: Text(
                          'جديد',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: controller.sized * 0.008,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Text(
                    'الرمز: $deviceId',
                    style: TextStyle(
                      color: AppColors.textColor.withOpacity(0.6),
                      fontSize: controller.sized * 0.01,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        SizedBox(height: controller.sized * 0.012),

        // Room selection
        Row(
          children: [
            Expanded(
              child: Container(
                padding:
                    EdgeInsets.symmetric(horizontal: controller.sized * 0.01),
                decoration: BoxDecoration(
                  color: AppColors.backgroundColor,
                  borderRadius: BorderRadius.circular(controller.sized * 0.006),
                  border: Border.all(
                    color: AppColors.textColor.withOpacity(0.2),
                    width: 1,
                  ),
                ),
                child: Directionality(
                  textDirection: TextDirection.rtl,
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      dropdownColor: AppColors.backgroundColor,
                      isExpanded: true,
                      value: devices[deviceId],
                      hint: Text(
                        'اختر الغرفة',
                        style: TextStyle(
                          color: AppColors.textColor.withOpacity(0.5),
                          fontSize: controller.sized * 0.011,
                        ),
                      ),
                      items: rooms1.keys
                          .map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(
                            rooms1[value].toString(),
                            style: TextStyle(
                              color: AppColors.textColor,
                              fontSize: controller.sized * 0.011,
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (s) async {
                        if (s != null) {
                          setDialogState(() {
                            devices[deviceId] = s;
                          });
                          setState(() {
                            devices[deviceId] = s;
                          });

                          await conn.query(
                            'update Devices set Rooms=? where id=?',
                            [s, deviceId],
                          );

                          final builder = MqttClientPayloadBuilder();
                          builder.addString('1');
                          client.publishMessage(
                              'edit', MqttQos.atLeastOnce, builder.payload!);
                          builder.clear();
                          builder.addString('re');
                          client.publishMessage(
                              deviceId, MqttQos.atLeastOnce, builder.payload!);
                        }
                      },
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: controller.sized * 0.008),
            Text(
              'الغرفة:',
              style: TextStyle(
                color: AppColors.textColor,
                fontSize: controller.sized * 0.011,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    ),
  );
}

// دالة لبناء بطاقة جهاز مخصص للحوار
Widget _buildDialogAssignedCard(
  dynamic device,
  Map<String, String> rooms,
  Map devices,
  MySqlConnection conn,
  Database appDB,
  Function setDialogState,
  Function setState,
  BuildContext context,
) {
  String deviceType = device.fields['Type'];
  String deviceId = device.fields['id'];
  String currentRoom = device.fields['rooms'];

  return Container(
    margin: EdgeInsets.only(bottom: controller.sized * 0.008),
    padding: EdgeInsets.all(controller.sized * 0.012),
    decoration: BoxDecoration(
      color: AppColors.backgroundColor2.withOpacity(0.3),
      borderRadius: BorderRadius.circular(controller.sized * 0.008),
      border: Border.all(
        color: AppColors.textColor.withOpacity(0.15),
        width: 1,
      ),
    ),
    child: Column(
      children: [
        // Header
        Row(
          children: [
            Icon(
              _getDeviceIcon(deviceType),
              color: AppColors.textColor.withOpacity(0.7),
              size: controller.sized * 0.018,
            ),
            SizedBox(width: controller.sized * 0.01),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getDeviceTypeName(deviceType),
                    style: TextStyle(
                      color: AppColors.textColor,
                      fontSize: controller.sized * 0.013,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'الرمز: $deviceId',
                    style: TextStyle(
                      color: AppColors.textColor.withOpacity(0.6),
                      fontSize: controller.sized * 0.01,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: controller.sized * 0.008,
                vertical: controller.sized * 0.003,
              ),
              decoration: BoxDecoration(
                color: AppColors.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(controller.sized * 0.006),
              ),
              child: Text(
                rooms[currentRoom] ?? 'غير محدد',
                style: TextStyle(
                  color: AppColors.primaryColor,
                  fontSize: controller.sized * 0.009,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: controller.sized * 0.012),

        // Actions
        Row(
          children: [
            // Delete button (if not ZAIN-Main)
            if (deviceType != 'ZAIN-Main')
              Expanded(
                child: TextButton.icon(
                  onPressed: () async {
                    Map d = {
                      'id': deviceId,
                      'device': deviceType,
                      'rooms': currentRoom,
                      'priv': deviceId,
                    };
                    del(context: context, i: d, appDB: appDB, conn: conn);
                  },
                  icon: Icon(
                    Icons.delete_outline,
                    color: AppColors.errorColor,
                    size: controller.sized * 0.014,
                  ),
                  label: Text(
                    'حذف',
                    style: TextStyle(
                      color: AppColors.errorColor,
                      fontSize: controller.sized * 0.01,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: controller.sized * 0.008,
                      vertical: controller.sized * 0.005,
                    ),
                    backgroundColor: AppColors.errorColor.withOpacity(0.1),
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(controller.sized * 0.006),
                    ),
                  ),
                ),
              )
            else
              Expanded(child: Container()),

            SizedBox(width: controller.sized * 0.01),

            // Room selection
            Expanded(
              flex: 2,
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: controller.sized * 0.008),
                      decoration: BoxDecoration(
                        color: AppColors.backgroundColor,
                        borderRadius:
                            BorderRadius.circular(controller.sized * 0.006),
                        border: Border.all(
                          color: AppColors.textColor.withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Directionality(
                        textDirection: TextDirection.rtl,
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            dropdownColor: AppColors.backgroundColor,
                            isExpanded: true,
                            value: devices[deviceId],
                            items: rooms.keys
                                .map<DropdownMenuItem<String>>((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(
                                  rooms[value].toString(),
                                  style: TextStyle(
                                    color: AppColors.textColor,
                                    fontSize: controller.sized * 0.01,
                                  ),
                                ),
                              );
                            }).toList(),
                            onChanged: (s) async {
                              if (s != null) {
                                setDialogState(() {
                                  devices[deviceId] = s;
                                });
                                setState(() {
                                  devices[deviceId] = s;
                                });

                                await conn.query(
                                  'update Devices set Rooms=? where id=?',
                                  [s, deviceId],
                                );

                                final builder = MqttClientPayloadBuilder();
                                builder.addString('1');
                                client.publishMessage('edit',
                                    MqttQos.atLeastOnce, builder.payload!);
                                builder.clear();
                                builder.addString('re');
                                client.publishMessage(deviceId,
                                    MqttQos.atLeastOnce, builder.payload!);
                              }
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: controller.sized * 0.006),
                  Text(
                    'نقل إلى:',
                    style: TextStyle(
                      color: AppColors.textColor,
                      fontSize: controller.sized * 0.01,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    ),
  );
}
