import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:sleek_circular_slider/sleek_circular_slider.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/themes/app_colors.dart';

HomeController controller = Get.put(HomeController(), permanent: true);

var deviceState;
var typeState;
var degree;
var speedState;
var swingState;

var sw;

var sil;
var volume;
var ch;

Widget addRoutineAc({
  required Map device,
  required Map roomData,
  setState1,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  var acPrivName = device['priv'];

  if (controller.addRoutine.containsKey(roomData['id']) &&
      controller.addRoutine[roomData['id']] != true &&
      controller.addRoutine[roomData['id']] != false &&
      controller.addRoutine[roomData['id']].containsKey(device['id'])) {
    if (controller.addRoutine[roomData['id']][device['id']]['state'] != null) {
      deviceState =
          controller.addRoutine[roomData['id']][device['id']]['state'];
    } else {
      deviceState = device['state'];
    }
    if (controller.addRoutine[roomData['id']][device['id']]['degree'] != null) {
      degree = controller.addRoutine[roomData['id']][device['id']]['degree'];
    } else {
      degree = device['degree'];
    }
    if (degree < 16) {
      degree = 16;
    }
    if (controller.addRoutine[roomData['id']][device['id']]['speed'] != null) {
      speedState = controller.addRoutine[roomData['id']][device['id']]['speed']
          .toString();
    } else {
      speedState = device['speed'].toString();
    }
    if (controller.addRoutine[roomData['id']][device['id']]['swing'] != null) {
      swingState = controller.addRoutine[roomData['id']][device['id']]['swing'];
    } else {
      swingState = device['swing'];
    }
    if (controller.addRoutine[roomData['id']][device['id']]['type'] != null) {
      typeState = controller.addRoutine[roomData['id']][device['id']]['type'];
    } else {
      typeState = device['type'];
    }
  } else {
    deviceState = device['state'];
    degree = device['degree'];
    speedState = device['speed'].toString();
    swingState = device['swing'];
    typeState = device['type'];
  }
  return StatefulBuilder(builder: ((context, setState) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Column(
        children: [
          SizedBox(
            height: controller.sizedHight * 0.02,
          ),
          Row(mainAxisSize: MainAxisSize.min, children: [
            containerIconsOption(
              content: Row(
                children: [
                  Directionality(
                    textDirection: TextDirection.rtl,
                    child: switchStyle(
                        size: controller.sized * 0.001,
                        value: deviceState,
                        onChanged: (s) {
                          setState(
                            () {
                              deviceState = s;
                            },
                          );

                          if (controller.addRoutine
                              .containsKey(roomData['id'])) {
                            if (controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id'])) {
                              if (controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] !=
                                  null) {
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['state'] = s;
                                if (s == false) {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['degree'] = null;
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['speed'] = null;
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['swing'] = null;
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['type'] = null;
                                }
                              }
                            }
                          }
                        }),
                  ),
                  IconButton(
                      onPressed: () {
                        if (controller.addRoutine.containsKey('home')) {
                          controller.addRoutine = {};
                        }
                        if (controller.addRoutine.containsKey(roomData['id'])) {
                          if (controller.addRoutine[roomData['id']] == true ||
                              controller.addRoutine[roomData['id']] == false) {
                            controller.addRoutine[roomData['id']] = {};
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {
                              'state': deviceState,
                              'degree': null,
                              'type': null,
                              'speed': null,
                              'swing': null
                            };
                          } else {
                            if (controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                              if (controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] ==
                                  null) {
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['state'] = deviceState;
                              } else {
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['state'] = null;

                                if (controller
                                    .addRoutine[roomData['id']][device['id']]
                                    .values
                                    .every((element) => element == null)) {
                                  controller.addRoutine[roomData['id']]
                                      .remove(device['id']);
                                  if (controller
                                      .addRoutine[roomData['id']].isEmpty) {
                                    controller.addRoutine
                                        .remove(roomData['id']);
                                  }
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': deviceState,
                                'degree': null,
                                'type': null,
                                'speed': null,
                                'swing': null
                              };
                            }
                          }
                        } else {
                          controller.addRoutine[roomData['id']] = {};
                          controller.addRoutine[roomData['id']]
                              [device['id']] = {
                            'state': deviceState,
                            'degree': null,
                            'type': null,
                            'speed': null,
                            'swing': null
                          };
                        }
                        setState1(
                          () {
                            controller.addRoutine;
                          },
                        );
                        print(controller.addRoutine);
                      },
                      icon: iconStyle(
                        icon: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] !=
                                    null
                            ? Icons.check_circle_rounded
                            : Icons.add_circle_outline_rounded,
                        color: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] !=
                                    null
                            ? AppColors.primaryColor
                            : AppColors.warningColor,
                      ))
                ],
              ),
            ),
            Expanded(
              child: Container(
                margin: EdgeInsets.zero,
                alignment: Alignment.bottomRight,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                        child: txtStyle(
                      txt: acPrivName!,
                    )),
                    SizedBox(
                      width: sized * 0.01,
                    ),
                    Container(
                      padding: EdgeInsets.only(left: sizedWidth * 0.01),
                      decoration: BoxDecoration(
                          border: Border(
                              left: BorderSide(
                                  color: AppColors.textColor.withOpacity(0.25),
                                  width: 1.5))),
                      child: txtStyle(
                        txt: 'مكيف هواء',
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: sizedWidth * 0.01,
            ),
            iconStyle(
              icon: Icons.ac_unit,
              color: AppColors.warningColor,
              size: sized * 0.035,
            ),
          ]),
          SizedBox(
            height: sizedHeight * 0.02,
          ),
          containerIconsOption(
            content: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          typeState = 'مروحة';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        print('1111111111111111111111111111');
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          print('2222222222222222222222222');
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['type'] !=
                              null) {
                            print('33333333333333333333333333333333333');
                            controller.addRoutine[roomData['id']][device['id']]
                                ['type'] = 'مروحة';
                            print(controller.addRoutine);
                          }
                        }
                      }
                      // acTypeState(2);
                    },
                    padding: EdgeInsets.symmetric(
                        horizontal: controller.sizedWidth * 0.05),
                    icon: iconStyle(
                      icon: Icons.air_rounded,
                      color: typeState == 'مروحة'
                          ? Color.fromARGB(255, 61, 182, 222)
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.04,
                    ),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.01,
                  ),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          typeState = 'تبريد';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['type'] !=
                              null) {
                            controller.addRoutine[roomData['id']][device['id']]
                                ['type'] = 'تبريد';
                          }
                        }
                      }
                      // acTypeState(1);
                    },
                    padding: EdgeInsets.symmetric(
                        horizontal: controller.sizedWidth * 0.05),
                    icon: iconStyle(
                      icon: Icons.ac_unit_rounded,
                      color: typeState == 'تبريد'
                          ? Colors.blue.shade600
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.04,
                    ),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.01,
                  ),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          typeState = 'تدفئة';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['type'] !=
                              null) {
                            controller.addRoutine[roomData['id']][device['id']]
                                ['type'] = 'تدفئة';
                          }
                        }
                      }
                      // acTypeState(0);
                    },
                    padding: EdgeInsets.symmetric(
                        horizontal: controller.sizedWidth * 0.05),
                    icon: iconStyle(
                      icon: Icons.wb_sunny_rounded,
                      color: typeState == 'تدفئة'
                          ? AppColors.warningColor
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.04,
                    ),
                  ),
                  SizedBox(
                      width: sizedWidth * 0.05, height: sizedHeight * 0.08),
                  Expanded(
                      child: IconButton(
                          onPressed: () {
                            if (controller.addRoutine.containsKey('home')) {
                              controller.addRoutine = {};
                            }
                            if (controller.addRoutine
                                .containsKey(roomData['id'])) {
                              if (controller.addRoutine[roomData['id']] ==
                                      true ||
                                  controller.addRoutine[roomData['id']] ==
                                      false) {
                                controller.addRoutine[roomData['id']] = {};
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  'state': true,
                                  'degree': null,
                                  'type': typeState,
                                  'speed': null,
                                  'swing': null
                                };
                              } else {
                                if (controller.addRoutine[roomData['id']]
                                    .containsKey(device['id'])) {
                                  if (controller.addRoutine[roomData['id']]
                                          [device['id']]['type'] ==
                                      null) {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['type'] = typeState;
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] = true;
                                  } else {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['type'] = null;
                                    if (controller
                                        .addRoutine[roomData['id']]
                                            [device['id']]
                                        .values
                                        .every((element) => element == null)) {
                                      controller.addRoutine[roomData['id']]
                                          .remove(device['id']);
                                      if (controller
                                          .addRoutine[roomData['id']].isEmpty) {
                                        controller.addRoutine
                                            .remove(roomData['id']);
                                      }
                                    }
                                  }
                                } else {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']] = {
                                    'state': true,
                                    'degree': null,
                                    'type': typeState,
                                    'speed': null,
                                    'swing': null
                                  };
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']] = {};
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': true,
                                'degree': null,
                                'type': typeState,
                                'speed': null,
                                'swing': null
                              };
                            }
                            setState1(
                              () {
                                controller.addRoutine;
                              },
                            );
                            print('66666666666666666666666666');
                            print(controller.addRoutine);
                          },
                          padding: EdgeInsets.only(
                              right: controller.sizedWidth * 0.0001),
                          icon: Center(
                            child: iconStyle(
                              icon: controller.addRoutine
                                          .containsKey(roomData['id']) &&
                                      controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id']) &&
                                      controller.addRoutine[roomData['id']]
                                              [device['id']]['type'] !=
                                          null
                                  ? Icons.check_circle_rounded
                                  : Icons.add_circle_outline_rounded,
                              color: controller.addRoutine
                                          .containsKey(roomData['id']) &&
                                      controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id']) &&
                                      controller.addRoutine[roomData['id']]
                                              [device['id']]['type'] !=
                                          null
                                  ? AppColors.primaryColor
                                  : AppColors.warningColor,
                            ),
                          ))),
                  SizedBox(
                    width: sizedWidth * 0.01,
                  ),
                ]),
          ),
          Directionality(
            textDirection: TextDirection.rtl,
            child: containerIconsOption(
              padding: EdgeInsets.only(
                  top: sizedHeight * 0.025, left: sizedWidth * 0.025),
              margin: EdgeInsets.only(top: sizedHeight * 0.025),
              content: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                      onPressed: () {
                        if (controller.addRoutine.containsKey('home')) {
                          controller.addRoutine = {};
                        }
                        if (controller.addRoutine.containsKey(roomData['id'])) {
                          if (controller.addRoutine[roomData['id']] == true ||
                              controller.addRoutine[roomData['id']] == false) {
                            controller.addRoutine[roomData['id']] = {};
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {
                              'state': true,
                              'degree': degree.toInt(),
                              'type': null,
                              'speed': null,
                              'swing': null
                            };
                          } else {
                            if (controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                              if (controller.addRoutine[roomData['id']]
                                      [device['id']]['degree'] ==
                                  null) {
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['degree'] = degree.toInt();
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['state'] = true;
                              } else {
                                controller.addRoutine[roomData['id']]
                                    [device['id']]['degree'] = null;
                                if (controller
                                    .addRoutine[roomData['id']][device['id']]
                                    .values
                                    .every((element) => element == null)) {
                                  controller.addRoutine[roomData['id']]
                                      .remove(device['id']);
                                  if (controller
                                      .addRoutine[roomData['id']].isEmpty) {
                                    controller.addRoutine
                                        .remove(roomData['id']);
                                  }
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': true,
                                'degree': degree.toInt(),
                                'type': null,
                                'speed': null,
                                'swing': null
                              };
                            }
                          }
                        } else {
                          controller.addRoutine[roomData['id']] = {};
                          controller.addRoutine[roomData['id']]
                              [device['id']] = {
                            'state': true,
                            'degree': degree.toInt(),
                            'type': null,
                            'speed': null,
                            'swing': null
                          };
                        }
                        setState1(
                          () {
                            controller.addRoutine;
                          },
                        );
                        print(controller.addRoutine);
                      },
                      padding: EdgeInsets.zero,
                      icon: iconStyle(
                        icon: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                        [device['id']]['degree'] !=
                                    null
                            ? Icons.check_circle_rounded
                            : Icons.add_circle_outline_rounded,
                        color: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                        [device['id']]['degree'] !=
                                    null
                            ? AppColors.primaryColor
                            : AppColors.warningColor,
                      )),
                  SleekCircularSlider(
                    min: 16,
                    max: 30,
                    initialValue: degree.toDouble(),
                    appearance: CircularSliderAppearance(
                        infoProperties: InfoProperties(
                            modifier: (percentage) => '${percentage.toInt()}°',
                            mainLabelStyle: TextStyle(
                                color: AppColors.textColor.withOpacity(0.35),
                                fontSize: sized * 0.05,
                                fontWeight: FontWeight.bold)),
                        size: (sized) * 0.14,
                        customColors: CustomSliderColors(
                            hideShadow: true,
                            trackColor: AppColors.textColor.withOpacity(0.25),
                            progressBarColors: <Color>[
                              Color(0xFF1AB600),
                              Color(0xFF6DD400),
                            ])),
                    onChangeEnd: (d) {
                      degree = d.toInt();
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['degree'] !=
                              null) {
                            controller.addRoutine[roomData['id']][device['id']]
                                ['degree'] = degree.toInt();
                          }
                        }
                      }
                    },
                  )
                ],
              ),
            ),
          ),
          txtStyle(
            txt: 'سرعه المروحة',
            color: AppColors.textColor.withOpacity(0.6),
            size: sized * 0.012,
          ),
          containerIconsOption(
            content: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  SizedBox(
                    width: sizedWidth * 0.01,
                  ),
                  txtStyle(
                    txt: '1',
                    color: speedState == '1'
                        ? Colors.blue.shade600
                        : AppColors.textColor.withOpacity(0.25),
                    size: sized * 0.013,
                  ),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          speedState = '1';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        print('1111111111111111111111111111');
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          print('2222222222222222222222222');
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['speed'] !=
                              null) {
                            print('33333333333333333333333333333333333');
                            controller.addRoutine[roomData['id']][device['id']]
                                ['speed'] = '1';
                            print(controller.addRoutine);
                          }
                        }
                      }
                      // acSpeedsState(1);
                    },
                    padding: EdgeInsets.zero,
                    icon: iconStyle(
                      icon: Icons.air_rounded,
                      color: speedState == '1'
                          ? Colors.blue.shade600
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.035,
                    ),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.023,
                  ),
                  txtStyle(
                    txt: '2',
                    color: speedState == '2'
                        ? Colors.blue.shade600
                        : AppColors.textColor.withOpacity(0.25),
                    size: sized * 0.013,
                  ),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          speedState = '2';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        print('1111111111111111111111111111');
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          print('2222222222222222222222222');
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['speed'] !=
                              null) {
                            print('33333333333333333333333333333333333');
                            controller.addRoutine[roomData['id']][device['id']]
                                ['speed'] = '2';
                            print(controller.addRoutine);
                          }
                        }
                      }
                      // acSpeedsState(2);
                    },
                    padding: EdgeInsets.zero,
                    icon: iconStyle(
                        icon: Icons.air_rounded,
                        color: speedState == '2'
                            ? Colors.blue.shade600
                            : AppColors.textColor.withOpacity(0.25),
                        size: sized * 0.035),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.023,
                  ),
                  txtStyle(
                      txt: '3',
                      color: speedState == '3'
                          ? Colors.blue.shade600
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.013),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          speedState = '3';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        print('1111111111111111111111111111');
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          print('2222222222222222222222222');
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['speed'] !=
                              null) {
                            print('33333333333333333333333333333333333');
                            controller.addRoutine[roomData['id']][device['id']]
                                ['speed'] = '3';
                            print(controller.addRoutine);
                          }
                        }
                      }
                      // acSpeedsState(3);
                    },
                    padding: EdgeInsets.zero,
                    icon: iconStyle(
                      icon: Icons.air_rounded,
                      color: speedState == '3'
                          ? Colors.blue.shade600
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.035,
                    ),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.023,
                  ),
                  IconButton(
                    onPressed: () {
                      setState(
                        () {
                          speedState = '4';
                        },
                      );
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        print('1111111111111111111111111111');
                        if (controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false &&
                            controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                          print('2222222222222222222222222');
                          if (controller.addRoutine[roomData['id']]
                                  [device['id']]['speed'] !=
                              null) {
                            print('33333333333333333333333333333333333');
                            controller.addRoutine[roomData['id']][device['id']]
                                ['speed'] = '4';
                            print(controller.addRoutine);
                          }
                        }
                      }
                      // acSpeedsState(4);
                    },
                    padding: EdgeInsets.zero,
                    icon: iconStyle(
                      icon: Icons.autorenew_rounded,
                      color: speedState == '4'
                          ? Colors.blue.shade600
                          : AppColors.textColor.withOpacity(0.25),
                      size: sized * 0.035,
                    ),
                  ),
                  SizedBox(
                    width: sizedWidth * 0.032,
                  ),
                  Expanded(
                    child: IconButton(
                        onPressed: () {
                          if (controller.addRoutine.containsKey('home')) {
                            controller.addRoutine = {};
                          }
                          if (controller.addRoutine
                              .containsKey(roomData['id'])) {
                            if (controller.addRoutine[roomData['id']] == true ||
                                controller.addRoutine[roomData['id']] ==
                                    false) {
                              controller.addRoutine[roomData['id']] = {};
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': true,
                                'degree': null,
                                'type': null,
                                'speed': speedState,
                                'swing': null
                              };
                            } else {
                              if (controller.addRoutine[roomData['id']]
                                  .containsKey(device['id'])) {
                                if (controller.addRoutine[roomData['id']]
                                        [device['id']]['speed'] ==
                                    null) {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['speed'] = speedState;
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] = true;
                                } else {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['speed'] = null;
                                  if (controller
                                      .addRoutine[roomData['id']][device['id']]
                                      .values
                                      .every((element) => element == null)) {
                                    controller.addRoutine[roomData['id']]
                                        .remove(device['id']);
                                    if (controller
                                        .addRoutine[roomData['id']].isEmpty) {
                                      controller.addRoutine
                                          .remove(roomData['id']);
                                    }
                                  }
                                }
                              } else {
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  'state': true,
                                  'degree': null,
                                  'type': null,
                                  'speed': speedState,
                                  'swing': null
                                };
                              }
                            }
                          } else {
                            controller.addRoutine[roomData['id']] = {};
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {
                              'state': true,
                              'degree': null,
                              'type': null,
                              'speed': speedState,
                              'swing': null
                            };
                          }
                          setState1(
                            () {
                              controller.addRoutine;
                            },
                          );
                          print(controller.addRoutine);
                        },
                        padding: EdgeInsets.zero,
                        icon: iconStyle(
                          icon: controller.addRoutine
                                      .containsKey(roomData['id']) &&
                                  controller.addRoutine[roomData['id']] !=
                                      true &&
                                  controller.addRoutine[roomData['id']] !=
                                      false &&
                                  controller.addRoutine[roomData['id']]
                                      .containsKey(device['id']) &&
                                  controller.addRoutine[roomData['id']]
                                          [device['id']]['speed'] !=
                                      null
                              ? Icons.check_circle_rounded
                              : Icons.add_circle_outline_rounded,
                          color: controller.addRoutine
                                      .containsKey(roomData['id']) &&
                                  controller.addRoutine[roomData['id']] !=
                                      true &&
                                  controller.addRoutine[roomData['id']] !=
                                      false &&
                                  controller.addRoutine[roomData['id']]
                                      .containsKey(device['id']) &&
                                  controller.addRoutine[roomData['id']]
                                          [device['id']]['speed'] !=
                                      null
                              ? AppColors.primaryColor
                              : AppColors.warningColor,
                        )),
                  )
                ]),
          ),
          containerIconsOption(
            padding: EdgeInsets.only(right: sizedWidth * 0.02),
            margin: EdgeInsets.only(top: sizedHeight * 0.015),
            content: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconButton(
                  onPressed: () {
                    setState(
                      () {
                        swingState = !swingState;
                      },
                    );

                    if (controller.addRoutine.containsKey(roomData['id'])) {
                      if (controller.addRoutine[roomData['id']] != true &&
                          controller.addRoutine[roomData['id']] != false &&
                          controller.addRoutine[roomData['id']]
                              .containsKey(device['id'])) {
                        if (controller.addRoutine[roomData['id']][device['id']]
                                ['swing'] !=
                            null) {
                          controller.addRoutine[roomData['id']][device['id']]
                              ['swing'] = swingState;
                        }
                      }
                    }
                  },
                  padding: EdgeInsets.zero,
                  icon: swingState
                      ? iconStyle(icon: Icons.check_box_rounded, color: AppColors.primaryColor)
                      : iconStyle(
                          icon: Icons.check_box_outline_blank_rounded,
                          color: AppColors.textColor2.withOpacity(0.25)),
                  iconSize: sized * 0.032,
                ),
                SizedBox(
                  width: sizedWidth * 0.01,
                ),
                txtStyle(
                  txt: 'التأرجح',
                  color: AppColors.textColor2.withOpacity(0.65),
                  size: sized * 0.015,
                ),
                SizedBox(
                  width: sizedWidth * 0.06,
                ),
                IconButton(
                    onPressed: () {
                      if (controller.addRoutine.containsKey('home')) {
                        controller.addRoutine = {};
                      }
                      if (controller.addRoutine.containsKey(roomData['id'])) {
                        if (controller.addRoutine[roomData['id']] == true ||
                            controller.addRoutine[roomData['id']] == false) {
                          controller.addRoutine[roomData['id']] = {};
                          controller.addRoutine[roomData['id']]
                              [device['id']] = {
                            'state': true,
                            'degree': null,
                            'type': null,
                            'speed': null,
                            'swing': swingState
                          };
                        } else {
                          if (controller.addRoutine[roomData['id']]
                              .containsKey(device['id'])) {
                            if (controller.addRoutine[roomData['id']]
                                    [device['id']]['swing'] ==
                                null) {
                              controller.addRoutine[roomData['id']]
                                  [device['id']]['swing'] = swingState;
                              controller.addRoutine[roomData['id']]
                                  [device['id']]['state'] = true;
                            } else {
                              controller.addRoutine[roomData['id']]
                                  [device['id']]['swing'] = null;

                              if (controller
                                  .addRoutine[roomData['id']][device['id']]
                                  .values
                                  .every((element) => element == null)) {
                                controller.addRoutine[roomData['id']]
                                    .remove(device['id']);
                                if (controller
                                    .addRoutine[roomData['id']].isEmpty) {
                                  controller.addRoutine.remove(roomData['id']);
                                }
                              }
                            }
                          } else {
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {
                              'state': true,
                              'degree': null,
                              'type': null,
                              'speed': null,
                              'swing': swingState
                            };
                          }
                        }
                      } else {
                        controller.addRoutine[roomData['id']] = {};
                        controller.addRoutine[roomData['id']][device['id']] = {
                          'state': true,
                          'degree': null,
                          'type': null,
                          'speed': null,
                          'swing': swingState
                        };
                      }
                      setState1(
                        () {
                          controller.addRoutine;
                        },
                      );
                      print(controller.addRoutine);
                    },
                    iconSize: controller.sized * 0.03,
                    icon: iconStyle(
                      icon: controller.addRoutine.containsKey(roomData['id']) &&
                              controller.addRoutine[roomData['id']] != true &&
                              controller.addRoutine[roomData['id']] != false &&
                              controller.addRoutine[roomData['id']]
                                  .containsKey(device['id']) &&
                              controller.addRoutine[roomData['id']]
                                      [device['id']]['swing'] !=
                                  null
                          ? Icons.check_circle_rounded
                          : Icons.add_circle_outline_rounded,
                      color: controller.addRoutine
                                  .containsKey(roomData['id']) &&
                              controller.addRoutine[roomData['id']] != true &&
                              controller.addRoutine[roomData['id']] != false &&
                              controller.addRoutine[roomData['id']]
                                  .containsKey(device['id']) &&
                              controller.addRoutine[roomData['id']]
                                      [device['id']]['swing'] !=
                                  null
                          ? AppColors.primaryColor
                          : AppColors.warningColor,
                    ))
              ],
            ),
          ),
          SizedBox(
            height: sizedHeight * 0.045,
          )
        ],
      ),
    );
  }));
}

Widget addRoutineSw({
  required Map device,
  required Map roomData,
  setState1,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  print(roomData);
  var swPrivName = device['priv'];

  if (controller.addRoutine.containsKey(roomData['id']) &&
      controller.addRoutine[roomData['id']] != true &&
      controller.addRoutine[roomData['id']] != false &&
      controller.addRoutine[roomData['id']].containsKey(device['id'])) {
    for (String v
        in controller.addRoutine[roomData['id']][device['id']].keys.toList()) {
      if (controller.addRoutine[roomData['id']][device['id']][v] != null) {
        sw[v] = controller.addRoutine[roomData['id']][device['id']][v];
      } else {
        sw[v] = device[v]['state'] ?? false;
      }
    }
  } else {
    for (String v in device.keys.toList().getRange(0, device.length - 4)) {
      sw[v] = device[v]['state'] ?? false;
    }
  }

  return StatefulBuilder(builder: ((context, setState) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Column(
        children: [
          SizedBox(
            height: controller.sizedHight * 0.02,
          ),
          Row(mainAxisSize: MainAxisSize.min, children: [
            containerIconsOption(
              content: Row(
                children: [
                  Directionality(
                    textDirection: TextDirection.rtl,
                    child: switchStyle(
                        size: controller.sized * 0.001,
                        value: sw.containsValue(true) == false ? false : true,
                        onChanged: (s) {
                          setState(
                            () {
                              for (var v in sw.keys) {
                                sw[v] = s;
                              }
                            },
                          );

                          if (controller.addRoutine
                              .containsKey(roomData['id'])) {
                            if (controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id'])) {
                              controller.addRoutine[roomData['id']]
                                      [device['id']]
                                  .forEach((key, value) {
                                if (value != null) {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']][key] = s;
                                }
                              });
                            }
                            print(controller.addRoutine);
                          }
                        }),
                  ),
                  IconButton(
                      onPressed: () {
                        if (controller.addRoutine.containsKey('home')) {
                          controller.addRoutine = {};
                        }
                        if (controller.addRoutine.containsKey(roomData['id'])) {
                          if (controller.addRoutine[roomData['id']] == true ||
                              controller.addRoutine[roomData['id']] == false) {
                            controller.addRoutine[roomData['id']] = {};
                            print(1111);
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {};
                            controller.addRoutine[roomData['id']][device['id']]
                                .addAll(sw);
                          } else {
                            if (controller.addRoutine[roomData['id']]
                                .containsKey(device['id'])) {
                              if (controller.addRoutine[roomData['id']]
                                          [device['id']]
                                      .containsValue(null) ==
                                  true) {
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {};
                                controller.addRoutine[roomData['id']]
                                        [device['id']]
                                    .addAll(sw);
                              } else {
                                controller.addRoutine[roomData['id']]
                                    .remove(device['id']);
                                if (controller
                                    .addRoutine[roomData['id']].isEmpty) {
                                  controller.addRoutine.remove(roomData['id']);
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {};
                              controller.addRoutine[roomData['id']]
                                      [device['id']]
                                  .addAll(sw);
                            }
                          }
                        } else {
                          controller.addRoutine[roomData['id']] = {};
                          controller.addRoutine[roomData['id']]
                              [device['id']] = {};
                          controller.addRoutine[roomData['id']][device['id']]
                              .addAll(sw);
                        }

                        setState1(
                          () {
                            controller.addRoutine;
                          },
                        );
                        print(controller.addRoutine);
                        print(sw);
                      },
                      iconSize: controller.sized * 0.03,
                      icon: iconStyle(
                        icon: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                            [device['id']]
                                        .containsValue(null) ==
                                    false
                            ? Icons.check_circle_rounded
                            : Icons.add_circle_outline_rounded,
                        color: controller.addRoutine
                                    .containsKey(roomData['id']) &&
                                controller.addRoutine[roomData['id']] != true &&
                                controller.addRoutine[roomData['id']] !=
                                    false &&
                                controller.addRoutine[roomData['id']]
                                    .containsKey(device['id']) &&
                                controller.addRoutine[roomData['id']]
                                            [device['id']]
                                        .containsValue(null) ==
                                    false
                            ? AppColors.primaryColor
                            : AppColors.warningColor,
                      ))
                ],
              ),
            ),
            Expanded(
              child: Container(
                margin: EdgeInsets.zero,
                alignment: Alignment.bottomRight,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                        child: txtStyle(
                      txt: swPrivName.split('_')[0] == 'x'
                          ? 'لا يوجد اسم'
                          : swPrivName.split('_')[0]!,
                    )),
                    SizedBox(
                      width: sized * 0.01,
                    ),
                    Container(
                      padding: EdgeInsets.only(left: sizedWidth * 0.01),
                      decoration: BoxDecoration(
                          border: Border(
                              left: BorderSide(
                                  color: AppColors.textColor.withOpacity(0.25),
                                  width: 1.5))),
                      child: txtStyle(
                        txt: 'مفاتيح',
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: sizedWidth * 0.01,
            ),
            iconStyle(
              icon: Icons.power_rounded,
              color: AppColors.warningColor,
              size: sized * 0.035,
            ),
          ]),
          SizedBox(
            height: sizedHeight * 0.02,
          ),
          Divider(
            color: AppColors.textColor2.withOpacity(0.3),
          ),
          Directionality(
            textDirection: TextDirection.rtl,
            child: ListView(
              shrinkWrap: true,
              children: [
                for (String v in sw.keys)
                  Container(
                    margin: EdgeInsets.symmetric(
                        horizontal: controller.sizedWidth * 0.01,
                        vertical: controller.sizedHight * 0.01),
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(Radius.circular(25)),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.only(
                                right: controller.sizedWidth * 0.03,
                                top: controller.sizedHight * 0.005),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                txtStyle(
                                  txt: v,
                                ),
                                txtStyle(
                                  txt: (device[v]['type'] == 'SWITCH'
                                          ? 'مفتاح : '
                                          : device[v]['type'] == 'VAN'
                                              ? 'مروحة : '
                                              : 'إضائة : ') +
                                      (device['priv'].split('_')[int.parse(
                                                  v.replaceAll('v', ''))] ==
                                              'x'
                                          ? 'لا يوجد اسم للمفتاح'
                                          : device['priv'].split('_')[int.parse(
                                              v.replaceAll('v', ''))]),
                                ),
                                Divider(
                                  color: AppColors.textColor2.withOpacity(0.5),
                                )
                              ],
                            ),
                          ),
                        ),
                        IconButton(
                            onPressed: () {
                              if (controller.addRoutine.containsKey('home')) {
                                controller.addRoutine = {};
                              }
                              if (controller.addRoutine
                                  .containsKey(roomData['id'])) {
                                if (controller.addRoutine[roomData['id']] ==
                                        true ||
                                    controller.addRoutine[roomData['id']] ==
                                        false) {
                                  controller.addRoutine[roomData['id']] = {};
                                  controller.addRoutine[roomData['id']]
                                      [device['id']] = {
                                    for (String vv in sw.keys)
                                      vv: vv == v ? sw[vv] : null
                                  };
                                } else {
                                  if (controller.addRoutine[roomData['id']]
                                      .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']][v] ==
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']][v] = sw[v];
                                    } else {
                                      print('1');
                                      print(sw);
                                      controller.addRoutine[roomData['id']]
                                          [device['id']][v] = null;
                                      print('11');
                                      print(sw);

                                      if (controller.addRoutine[roomData['id']]
                                                      [device['id']]
                                                  .containsValue(true) ==
                                              false &&
                                          controller.addRoutine[roomData['id']]
                                                      [device['id']]
                                                  .containsValue(false) ==
                                              false) {
                                        controller.addRoutine[roomData['id']]
                                            .remove(device['id']);
                                        if (controller
                                            .addRoutine[roomData['id']]
                                            .isEmpty) {
                                          controller.addRoutine
                                              .remove(roomData['id']);
                                        }
                                      }
                                    }
                                  } else {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']] = {
                                      for (String vv in sw.keys)
                                        vv: vv == v ? sw[vv] : null
                                    };
                                  }
                                }
                              } else {
                                controller.addRoutine[roomData['id']] = {};
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  for (String vv in sw.keys)
                                    vv: vv == v ? sw[vv] : null
                                };
                              }
                              print('5555555555555555555555555555');
                              print(sw);
                              setState1(
                                () {
                                  controller.addRoutine;
                                },
                              );
                              print(controller.addRoutine);
                              print(sw);
                            },
                            icon: iconStyle(
                              icon: controller.addRoutine
                                          .containsKey(roomData['id']) &&
                                      controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id']) &&
                                      controller.addRoutine[roomData['id']]
                                              [device['id']][v] !=
                                          null
                                  ? Icons.check_circle_rounded
                                  : Icons.add_circle_outline_rounded,
                              color: controller.addRoutine
                                          .containsKey(roomData['id']) &&
                                      controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id']) &&
                                      controller.addRoutine[roomData['id']]
                                              [device['id']][v] !=
                                          null
                                  ? AppColors.primaryColor
                                  : AppColors.warningColor,
                            )),
                        Directionality(
                          textDirection: TextDirection.rtl,
                          child: switchStyle(
                              size: controller.sized * 0.001,
                              value: sw[v],
                              onChanged: (s) {
                                setState(
                                  () {
                                    sw[v] = s;
                                  },
                                );

                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']][v] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']][v] = s;
                                    }
                                  }
                                }
                                print(controller.addRoutine);
                                print(sw);
                              }),
                        ),
                      ],
                    ),
                  )
              ],
            ),
          ),
        ],
      ),
    );
  }));
}

Widget addRoutineTv({
  required Map device,
  required Map roomData,
  setState1,
  required double sizedWidth,
  required double sizedHeight,
  required double sized,
}) {
  print(device);
  var tvPrivName = device['priv'];

  if (controller.addRoutine.containsKey(roomData['id']) &&
      controller.addRoutine[roomData['id']] != true &&
      controller.addRoutine[roomData['id']] != false &&
      controller.addRoutine[roomData['id']].containsKey(device['id'])) {
    if (controller.addRoutine[roomData['id']][device['id']]['state'] != null) {
      deviceState =
          controller.addRoutine[roomData['id']][device['id']]['state'];
    } else {
      deviceState = device['state'];
    }
    if (controller.addRoutine[roomData['id']][device['id']]['ch'] != null) {
      ch = controller.addRoutine[roomData['id']][device['id']]['ch'];
    } else {
      ch = '+1';
    }
    if (controller.addRoutine[roomData['id']][device['id']]['v'] != null) {
      volume = controller.addRoutine[roomData['id']][device['id']]['v'];
    } else {
      volume = '+1';
    }
  } else {
    deviceState = device['state'];
    sil = device['sil'];
    ch = '+1';
    volume = '+1';
  }

  return StatefulBuilder(builder: ((context, setState) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(
              height: controller.sizedHight * 0.02,
            ),
            Row(mainAxisSize: MainAxisSize.min, children: [
              containerIconsOption(
                content: Row(
                  children: [
                    Directionality(
                      textDirection: TextDirection.rtl,
                      child: switchStyle(
                          size: controller.sized * 0.001,
                          value: deviceState,
                          onChanged: (s) {
                            setState(
                              () {
                                deviceState = s;
                              },
                            );

                            if (controller.addRoutine
                                .containsKey(roomData['id'])) {
                              if (controller.addRoutine[roomData['id']] !=
                                      true &&
                                  controller.addRoutine[roomData['id']] !=
                                      false &&
                                  controller.addRoutine[roomData['id']]
                                      .containsKey(device['id'])) {
                                if (controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] !=
                                    null) {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] = s;
                                  if (s == false) {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['ch'] = null;
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['v'] = null;
                                  }
                                }
                              }
                            }
                          }),
                    ),
                    IconButton(
                        onPressed: () {
                          if (controller.addRoutine.containsKey('home')) {
                            controller.addRoutine = {};
                          }
                          if (controller.addRoutine
                              .containsKey(roomData['id'])) {
                            if (controller.addRoutine[roomData['id']] == true ||
                                controller.addRoutine[roomData['id']] ==
                                    false) {
                              controller.addRoutine[roomData['id']] = {};
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': deviceState,
                                'ch': null,
                                'v': null,
                              };
                            } else {
                              if (controller.addRoutine[roomData['id']]
                                  .containsKey(device['id'])) {
                                if (controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] ==
                                    null) {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] = deviceState;
                                } else {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['state'] = null;
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['ch'] = null;
                                  controller.addRoutine[roomData['id']]
                                      [device['id']]['v'] = null;

                                  if (controller
                                      .addRoutine[roomData['id']][device['id']]
                                      .values
                                      .every((element) => element == null)) {
                                    controller.addRoutine[roomData['id']]
                                        .remove(device['id']);
                                    if (controller
                                        .addRoutine[roomData['id']].isEmpty) {
                                      controller.addRoutine
                                          .remove(roomData['id']);
                                    }
                                  }
                                }
                              } else {
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  'state': deviceState,
                                  'ch': null,
                                  'v': null,
                                };
                              }
                            }
                          } else {
                            controller.addRoutine[roomData['id']] = {};
                            controller.addRoutine[roomData['id']]
                                [device['id']] = {
                              'state': deviceState,
                              'ch': null,
                              'v': null,
                            };
                          }
                          setState1(
                            () {
                              controller.addRoutine;
                            },
                          );
                          print(controller.addRoutine);
                        },
                        icon: iconStyle(
                          icon: controller.addRoutine
                                      .containsKey(roomData['id']) &&
                                  controller.addRoutine[roomData['id']] !=
                                      true &&
                                  controller.addRoutine[roomData['id']] !=
                                      false &&
                                  controller.addRoutine[roomData['id']]
                                      .containsKey(device['id']) &&
                                  controller.addRoutine[roomData['id']]
                                          [device['id']]['state'] !=
                                      null
                              ? Icons.check_circle_rounded
                              : Icons.add_circle_outline_rounded,
                          color: controller.addRoutine
                                      .containsKey(roomData['id']) &&
                                  controller.addRoutine[roomData['id']] !=
                                      true &&
                                  controller.addRoutine[roomData['id']] !=
                                      false &&
                                  controller.addRoutine[roomData['id']]
                                      .containsKey(device['id']) &&
                                  controller.addRoutine[roomData['id']]
                                          [device['id']]['state'] !=
                                      null
                              ? AppColors.primaryColor
                              : AppColors.warningColor,
                        ))
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  margin: EdgeInsets.zero,
                  alignment: Alignment.bottomRight,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                          child: txtStyle(
                        txt: tvPrivName!,
                      )),
                      SizedBox(
                        width: sized * 0.01,
                      ),
                      Container(
                        padding: EdgeInsets.only(left: sizedWidth * 0.01),
                        decoration: BoxDecoration(
                            border: Border(
                                left: BorderSide(
                                    color: AppColors.textColor.withOpacity(0.25),
                                    width: 1.5))),
                        child: txtStyle(
                          txt: 'تلفاز',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                width: sizedWidth * 0.01,
              ),
              iconStyle(
                icon: Icons.tv_rounded,
                color: AppColors.warningColor,
                size: sized * 0.035,
              ),
            ]),
            SizedBox(
              height: sizedHeight * 0.05,
            ),
            txtStyle(
              txt: 'القنوات',
            ),
            Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                  border:
                      Border.all(color: AppColors.textColor.withOpacity(0.25), width: 1.5)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                              onPressed: () async {
                                if (client.connectionStatus!.state.name ==
                                    'connected') {
                                  Map chS = device['ch'];
                                  var selectitem = 0;
                                  if (chS.length == 0) {}
                                  TextEditingController name =
                                      TextEditingController(
                                    text: '',
                                  );
                                  TextEditingController number =
                                      TextEditingController(
                                    text: '',
                                  );
                                  bool edit = false;
                                  bool numEdit = false;
                                  String selectname = '';
                                  if (chS.length != 0) {
                                    selectname = chS.keys.toList()[0];
                                  }

                                  AwesomeDialog(
                                    context: context,
                                    dialogType: DialogType.noHeader,
                                    headerAnimationLoop: true,
                                    animType: AnimType.topSlide,
                                    dialogBackgroundColor: AppColors.backgroundColor2,
                                    body: chS.length != 0
                                        ? Column(
                                            children: [
                                              SizedBox(
                                                height: controller.sizedHight *
                                                    0.35,
                                                child: CupertinoPicker(
                                                  squeeze: 0.9,
                                                  // scrollController:
                                                  //     FixedExtentScrollController(
                                                  //         initialItem:
                                                  //             3),
                                                  itemExtent:
                                                      40, //height of each item

                                                  looping: false,

                                                  magnification: 1.2,
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  children: <Widget>[
                                                    for (var c in chS.keys)
                                                      Padding(
                                                        padding: EdgeInsets.symmetric(
                                                            horizontal: controller
                                                                    .sizedWidth *
                                                                0.2),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .center,
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .center,
                                                          mainAxisSize:
                                                              MainAxisSize.min,
                                                          children: [
                                                            Expanded(
                                                              child: Text(
                                                                  chS[c],
                                                                  style:
                                                                      TextStyle(
                                                                    color: AppColors.textColor
                                                                        .withOpacity(
                                                                            0.8),
                                                                    fontSize:
                                                                        controller.sized *
                                                                            0.016,
                                                                    fontWeight:
                                                                        FontWeight
                                                                            .bold,
                                                                  )),
                                                            ),
                                                            Text(
                                                              c,
                                                              style: TextStyle(
                                                                color: AppColors.textColor
                                                                    .withOpacity(
                                                                        0.8),
                                                                fontSize: controller
                                                                        .sized *
                                                                    0.016,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                  ],
                                                  onSelectedItemChanged:
                                                      (int index) {
                                                    selectname = chS.keys
                                                        .toList()[index];
                                                  },
                                                  // onSelectedItemChanged: (int index) {
                                                  //   selectitem = index;
                                                  // },
                                                ),
                                              ),
                                              SizedBox(
                                                height: controller.sizedHight *
                                                    0.025,
                                              ),
                                              Material(
                                                color: Colors.transparent,
                                                child: CircleAvatar(
                                                    radius: controller.sized *
                                                        0.025,
                                                    backgroundColor: AppColors.primaryColor,
                                                    child: IconButton(
                                                      padding:
                                                          const EdgeInsets.only(
                                                              left: 10),
                                                      onPressed: () {
                                                        setState(
                                                          () {
                                                            ch =
                                                                chS[selectname];
                                                          },
                                                        );

                                                        if (controller
                                                            .addRoutine
                                                            .containsKey(
                                                                roomData[
                                                                    'id'])) {
                                                          if (controller.addRoutine[
                                                                      roomData[
                                                                          'id']] !=
                                                                  true &&
                                                              controller.addRoutine[
                                                                      roomData[
                                                                          'id']] !=
                                                                  false &&
                                                              controller
                                                                  .addRoutine[
                                                                      roomData[
                                                                          'id']]
                                                                  .containsKey(
                                                                      device[
                                                                          'id'])) {
                                                            if (controller
                                                                        .addRoutine[
                                                                    roomData[
                                                                        'id']][device[
                                                                    'id']]['ch'] !=
                                                                null) {
                                                              controller
                                                                      .addRoutine[
                                                                  roomData[
                                                                      'id']][device[
                                                                  'id']]['ch'] = ch;
                                                            }
                                                          }
                                                        }

                                                        Navigator.of(context)
                                                            .pop();
                                                      },
                                                      icon: Icon(
                                                        Icons.arrow_back_ios,
                                                        size: controller.sized *
                                                            0.038,
                                                        color: AppColors.backgroundColor2,
                                                      ),
                                                    )),
                                              ),
                                              SizedBox(
                                                height: controller.sizedHight *
                                                    0.025,
                                              )
                                            ],
                                          )
                                        : Text(
                                            'لا يوجد اسماء قنوات مفضلة',
                                            style: TextStyle(
                                              color: AppColors.textColor.withOpacity(0.8),
                                              fontSize:
                                                  controller.sized * 0.016,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),

                                    // btnCancelOnPress:
                                    //     () {},
                                  ).show();
                                }
                              },
                              icon: Icon(Icons.important_devices_rounded),
                              iconSize: sized * 0.035,
                              color: AppColors.textColor.withOpacity(0.7)),
                          IconButton(
                              onPressed: () {
                                TextEditingController ch123 =
                                    TextEditingController();
                                if (client.connectionStatus!.state.name ==
                                    'connected') {
                                  AwesomeDialog(
                                      context: context,
                                      dialogType: DialogType.noHeader,
                                      headerAnimationLoop: true,
                                      animType: AnimType.topSlide,
                                      dialogBackgroundColor: AppColors.backgroundColor2,
                                      body: Material(
                                        color: Colors.transparent,
                                        child: Container(
                                          child: Column(
                                            children: [
                                              TextField(
                                                autofocus: true,
                                                controller: ch123,
                                                cursorWidth: 0,
                                                style: TextStyle(
                                                    color: AppColors.textColor
                                                        .withOpacity(0.7),
                                                    fontSize: 25,
                                                    fontWeight:
                                                        FontWeight.bold),
                                                textInputAction:
                                                    TextInputAction.done,
                                                textAlign: TextAlign.center,
                                                decoration: InputDecoration(
                                                    hintStyle: TextStyle(
                                                        color: AppColors.textColor
                                                            .withOpacity(0.7)),
                                                    border:
                                                        const UnderlineInputBorder(
                                                            borderSide:
                                                                BorderSide
                                                                    .none),
                                                    hintText: "ادخل الرقم"),
                                                keyboardType:
                                                    TextInputType.number,
                                                maxLength: 10,
                                              ),
                                              CircleAvatar(
                                                  radius:
                                                      controller.sized * 0.025,
                                                  backgroundColor: AppColors.primaryColor,
                                                  child: IconButton(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            left: 10),
                                                    onPressed: () {
                                                      if (ch123
                                                          .text.isNumericOnly) {
                                                        setState(() {
                                                          ch = ch123.text;
                                                        });
                                                        if (controller
                                                            .addRoutine
                                                            .containsKey(
                                                                roomData[
                                                                    'id'])) {
                                                          if (controller.addRoutine[
                                                                      roomData[
                                                                          'id']] !=
                                                                  true &&
                                                              controller.addRoutine[
                                                                      roomData[
                                                                          'id']] !=
                                                                  false &&
                                                              controller
                                                                  .addRoutine[
                                                                      roomData[
                                                                          'id']]
                                                                  .containsKey(
                                                                      device[
                                                                          'id'])) {
                                                            if (controller
                                                                        .addRoutine[
                                                                    roomData[
                                                                        'id']][device[
                                                                    'id']]['ch'] !=
                                                                null) {
                                                              controller
                                                                      .addRoutine[
                                                                  roomData[
                                                                      'id']][device[
                                                                  'id']]['ch'] = ch;
                                                            }
                                                          }
                                                        }
                                                        Navigator.of(context)
                                                            .pop();
                                                      }
                                                    },
                                                    icon: Icon(
                                                      Icons.arrow_back_ios,
                                                      size: controller.sized *
                                                          0.038,
                                                      color: AppColors.backgroundColor2
                                                          .withOpacity(0.85),
                                                    ),
                                                  )),
                                              SizedBox(
                                                height: controller.sizedHight *
                                                    0.02,
                                              )
                                            ],
                                          ),
                                        ),
                                      )).show();
                                }
                              },
                              icon: Icon(Icons.pin_rounded),
                              iconSize: sized * 0.035,
                              color: AppColors.textColor.withOpacity(0.7)),
                        ],
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                              onPressed: () {
                                setState((() {
                                  if (ch.contains('-') || ch.contains('+')) {
                                    ch = int.parse(ch) + 1;
                                    if (ch > 0) {
                                      ch = '+' + ch.toString();
                                    } else if (ch == 0) {
                                      ch = '+1';
                                    } else {
                                      ch = ch.toString();
                                    }
                                  } else {
                                    ch = '+1';
                                  }
                                }));
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['ch'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['ch'] = ch;
                                    }
                                  }
                                }
                              },
                              iconSize: controller.sized * 0.033,
                              color: AppColors.primaryColor,
                              icon: Icon(Icons.add_box_rounded)),
                          IconButton(
                              onPressed: () {
                                setState((() {
                                  if (ch.contains('-') || ch.contains('+')) {
                                    ch = int.parse(ch) - 1;
                                    if (ch > 0) {
                                      ch = '+' + ch.toString();
                                    } else if (ch == 0) {
                                      ch = '-1';
                                    } else {
                                      ch = ch.toString();
                                    }
                                  } else {
                                    ch = '-1';
                                  }
                                }));
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['ch'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['ch'] = ch;
                                    }
                                  }
                                }
                              },
                              iconSize: controller.sized * 0.033,
                              color: Colors.blue,
                              icon:
                                  Icon(Icons.indeterminate_check_box_rounded)),
                        ],
                      ),
                      SizedBox(
                        width: controller.sizedWidth * 0.05,
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.textColor2.withOpacity(0.05),
                          borderRadius: BorderRadius.all(Radius.circular(25.5)),
                        ),
                        height: controller.sizedHight * 0.15,
                        width: controller.sizedWidth * 0.3,
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: Center(
                            child: Text(ch.toString(),
                                style: TextStyle(
                                  // fontSize: double.infinity,
                                  color: AppColors.textColor2.withOpacity(0.7),
                                )),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      IconButton(
                          onPressed: () {
                            if (controller.addRoutine.containsKey('home')) {
                              controller.addRoutine = {};
                            }
                            if (controller.addRoutine
                                .containsKey(roomData['id'])) {
                              if (controller.addRoutine[roomData['id']] ==
                                      true ||
                                  controller.addRoutine[roomData['id']] ==
                                      false) {
                                controller.addRoutine[roomData['id']] = {};
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  'state': true,
                                  'ch': ch,
                                  'v': null,
                                };
                              } else {
                                if (controller.addRoutine[roomData['id']]
                                    .containsKey(device['id'])) {
                                  if (controller.addRoutine[roomData['id']]
                                          [device['id']]['ch'] ==
                                      null) {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['ch'] = ch;
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] = true;
                                  } else {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['ch'] = null;

                                    if (controller
                                        .addRoutine[roomData['id']]
                                            [device['id']]
                                        .values
                                        .every((element) => element == null)) {
                                      controller.addRoutine[roomData['id']]
                                          .remove(device['id']);
                                      if (controller
                                          .addRoutine[roomData['id']].isEmpty) {
                                        controller.addRoutine
                                            .remove(roomData['id']);
                                      }
                                    }
                                  }
                                } else {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']] = {
                                    'state': true,
                                    'ch': ch,
                                    'v': null,
                                  };
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']] = {};
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': true,
                                'ch': ch,
                                'v': null,
                              };
                            }
                            setState1(
                              () {
                                controller.addRoutine;
                              },
                            );
                            print(controller.addRoutine);
                          },
                          icon: iconStyle(
                            icon: controller.addRoutine
                                        .containsKey(roomData['id']) &&
                                    controller.addRoutine[roomData['id']] !=
                                        true &&
                                    controller.addRoutine[roomData['id']] !=
                                        false &&
                                    controller.addRoutine[roomData['id']]
                                        .containsKey(device['id']) &&
                                    controller.addRoutine[roomData['id']]
                                            [device['id']]['ch'] !=
                                        null
                                ? Icons.check_circle_rounded
                                : Icons.add_circle_outline_rounded,
                            color: controller.addRoutine
                                        .containsKey(roomData['id']) &&
                                    controller.addRoutine[roomData['id']] !=
                                        true &&
                                    controller.addRoutine[roomData['id']] !=
                                        false &&
                                    controller.addRoutine[roomData['id']]
                                        .containsKey(device['id']) &&
                                    controller.addRoutine[roomData['id']]
                                            [device['id']]['ch'] !=
                                        null
                                ? AppColors.primaryColor
                                : AppColors.warningColor,
                          )),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                ],
              ),
            ),
            SizedBox(
              height: sizedHeight * 0.05,
            ),
            txtStyle(
              txt: 'الصوت',
            ),
            Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                  border:
                      Border.all(color: AppColors.textColor.withOpacity(0.25), width: 1.5)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      volume == true
                          ? IconButton(
                              onPressed: () {
                                setState(
                                  () {
                                    volume = false;
                                  },
                                );
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['v'] = volume;
                                    }
                                  }
                                }
                              },
                              icon: Icon(Icons.volume_off_rounded),
                              iconSize: sized * 0.035,
                              color: AppColors.textColor.withOpacity(0.7))
                          : IconButton(
                              onPressed: () {
                                setState(
                                  () {
                                    volume = true;
                                  },
                                );
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['v'] = volume;
                                    }
                                  }
                                }
                              },
                              icon: Icon(Icons.volume_up_rounded),
                              iconSize: sized * 0.035,
                              color: AppColors.textColor.withOpacity(0.7)),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                              onPressed: () {
                                setState((() {
                                  if (volume != true && volume != false) {
                                    volume = int.parse(volume) + 1;
                                    if (volume > 0) {
                                      volume = '+' + volume.toString();
                                    } else if (volume == 0) {
                                      volume = '+1';
                                    } else {
                                      volume = volume.toString();
                                    }
                                  } else {
                                    volume = '+1';
                                  }
                                }));
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['v'] = volume;
                                    }
                                  }
                                }
                              },
                              iconSize: controller.sized * 0.033,
                              color: AppColors.primaryColor,
                              icon: Icon(Icons.add_box_rounded)),
                          IconButton(
                              onPressed: () {
                                setState((() {
                                  if (volume != true && volume != false) {
                                    volume = int.parse(volume) - 1;
                                    if (volume > 0) {
                                      volume = '+' + volume.toString();
                                    } else if (volume == 0) {
                                      volume = '-1';
                                    } else {
                                      volume = volume.toString();
                                    }
                                  } else {
                                    volume = '-1';
                                  }
                                }));
                                if (controller.addRoutine
                                    .containsKey(roomData['id'])) {
                                  if (controller.addRoutine[roomData['id']] !=
                                          true &&
                                      controller.addRoutine[roomData['id']] !=
                                          false &&
                                      controller.addRoutine[roomData['id']]
                                          .containsKey(device['id'])) {
                                    if (controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null) {
                                      controller.addRoutine[roomData['id']]
                                          [device['id']]['v'] = volume;
                                    }
                                  }
                                }
                              },
                              iconSize: controller.sized * 0.033,
                              color: Colors.blue,
                              icon:
                                  Icon(Icons.indeterminate_check_box_rounded)),
                        ],
                      ),
                      SizedBox(
                        width: controller.sizedWidth * 0.05,
                      ),
                      Container(
                        decoration: BoxDecoration(
                          color: AppColors.textColor2.withOpacity(0.05),
                          borderRadius: BorderRadius.all(Radius.circular(25.5)),
                        ),
                        height: controller.sizedHight * 0.15,
                        width: controller.sizedWidth * 0.3,
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: Center(
                            child: volume == true || volume == false
                                ? Icon(
                                    volume == true
                                        ? Icons.volume_off_rounded
                                        : Icons.volume_up_rounded,
                                    color: AppColors.textColor.withOpacity(0.7))
                                : Text(volume.toString(),
                                    style: TextStyle(
                                      // fontSize: double.infinity,
                                      color: AppColors.textColor2.withOpacity(0.7),
                                    )),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                      IconButton(
                          onPressed: () {
                            if (controller.addRoutine.containsKey('home')) {
                              controller.addRoutine = {};
                            }
                            if (controller.addRoutine
                                .containsKey(roomData['id'])) {
                              if (controller.addRoutine[roomData['id']] ==
                                      true ||
                                  controller.addRoutine[roomData['id']] ==
                                      false) {
                                controller.addRoutine[roomData['id']] = {};
                                controller.addRoutine[roomData['id']]
                                    [device['id']] = {
                                  'state': true,
                                  'ch': null,
                                  'v': volume,
                                };
                              } else {
                                if (controller.addRoutine[roomData['id']]
                                    .containsKey(device['id'])) {
                                  if (controller.addRoutine[roomData['id']]
                                          [device['id']]['v'] ==
                                      null) {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['v'] = volume;
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['state'] = true;
                                  } else {
                                    controller.addRoutine[roomData['id']]
                                        [device['id']]['v'] = null;

                                    if (controller
                                        .addRoutine[roomData['id']]
                                            [device['id']]
                                        .values
                                        .every((element) => element == null)) {
                                      controller.addRoutine[roomData['id']]
                                          .remove(device['id']);
                                      if (controller
                                          .addRoutine[roomData['id']].isEmpty) {
                                        controller.addRoutine
                                            .remove(roomData['id']);
                                      }
                                    }
                                  }
                                } else {
                                  controller.addRoutine[roomData['id']]
                                      [device['id']] = {
                                    'state': true,
                                    'ch': null,
                                    'v': volume,
                                  };
                                }
                              }
                            } else {
                              controller.addRoutine[roomData['id']] = {};
                              controller.addRoutine[roomData['id']]
                                  [device['id']] = {
                                'state': true,
                                'ch': null,
                                'v': volume,
                              };
                            }
                            setState1(
                              () {
                                controller.addRoutine;
                              },
                            );
                            print(controller.addRoutine);
                          },
                          icon: iconStyle(
                            icon: controller.addRoutine
                                        .containsKey(roomData['id']) &&
                                    controller.addRoutine[roomData['id']] !=
                                        true &&
                                    controller.addRoutine[roomData['id']] !=
                                        false &&
                                    controller.addRoutine[roomData['id']]
                                        .containsKey(device['id']) &&
                                    controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null
                                ? Icons.check_circle_rounded
                                : Icons.add_circle_outline_rounded,
                            color: controller.addRoutine
                                        .containsKey(roomData['id']) &&
                                    controller.addRoutine[roomData['id']] !=
                                        true &&
                                    controller.addRoutine[roomData['id']] !=
                                        false &&
                                    controller.addRoutine[roomData['id']]
                                        .containsKey(device['id']) &&
                                    controller.addRoutine[roomData['id']]
                                            [device['id']]['v'] !=
                                        null
                                ? AppColors.primaryColor
                                : AppColors.warningColor,
                          )),
                      SizedBox(
                        width: controller.sizedWidth * 0.02,
                      ),
                    ],
                  ),
                  SizedBox(
                    height: controller.sizedHight * 0.02,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }));
}
