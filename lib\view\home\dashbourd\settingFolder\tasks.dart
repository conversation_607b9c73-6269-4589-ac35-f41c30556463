import 'dart:convert';

import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:get/get.dart';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mysql1/mysql1.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/material.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/modules/local/alarm.dart';
import 'package:zaen/modules/local/mqtt.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/view/home/<USER>/routine.dart';
import 'package:zaen/shared/themes/app_colors.dart';

tasks(context, setState) async {
  if (client.connectionStatus!.state.name == 'connected') {
    final conn = await MySqlConnection.connect(ConnectionSettings(
        host: controller.hostZain.value,
        // port: 80,
        user: 'root',
        db: 'zain',
        password: 'zain',
        characterSet: CharacterSet.UTF8));

    var appDB = await openDatabase('${controller.system}.db', version: 3);
    print(controller.tasks);
    for (var t in controller.tasks!) {
      print(t.fields);
    }

    // Map<String, String> rooms = {};
    // Results appdevices = await conn.query("SELECT id,Type,rooms FROM Devices");
    // print(appdevices);
    // Map devices = {};

    // for (var r = 0; r < controller.rooms.keys.toList().length; r++) {
    //   rooms[controller.rooms[controller.rooms.keys.toList()[r]]['id']] =
    //       controller.rooms[controller.rooms.keys.toList()[r]]['privName'];
    //   for (var d in controller
    //       .rooms[controller.rooms.keys.toList()[r]]['devices'].values) {
    //     devices[d['id']] =
    //         controller.rooms[controller.rooms.keys.toList()[r]]['id'];
    //   }
    // }
    // for (var r in appdevices) {
    //   if (r.fields['rooms'] == 'x') {
    //     devices[r.fields['id']] = 'x';
    //   }
    // }
    // Map<String, String> rooms1 = {};
    // rooms1['x'] = 'بدون غرفة';
    // rooms1.addAll(rooms);
    // print(devices);
    // print('1111111111111111111111111111111111111');

    AwesomeDialog(
        context: context,
        dialogType: DialogType.noHeader,
        headerAnimationLoop: true,
        dialogBackgroundColor: AppColors.backgroundColor2,
        animType: AnimType.topSlide,
        width: controller.sizedWidth,
        padding: EdgeInsets.only(
            bottom: controller.sizedHight * 0.02,
            right: controller.sizedWidth * 0.01,
            left: controller.sizedWidth * 0.01),
        body: GetBuilder<HomeController>(
            builder: ((controller) => Material(
                color: Colors.transparent,
                child: Column(
                  children: [
                    txtStyle(txt: 'المهام المجدولة'),
                    GestureDetector(
                      onTap: () async {
                        if (Navigator.of(context).canPop()) {
                          Navigator.of(context).pop();
                        }
                        await 0.3.delay();
                        setState(() {
                          p = 1;
                          controller.addRoutine.clear();
                          isSetting = true;
                          isShortcut = false;
                          isTask = true;
                          isWords = false;
                          add = true;
                          myId = '';
                        });
                        pageController.jumpToPage(p);
                      },
                      child: containerIconsOption(
                        radius: 10,
                        margin: EdgeInsets.all(controller.sized * 0.01),
                        padding: EdgeInsets.all(controller.sized * 0.002),
                        content: Container(
                          width: double.infinity,
                          child: iconStyle(
                              icon: Icons.add_circle_rounded,
                              color: AppColors.primaryColor.withOpacity(0.5),
                              size: controller.sized * 0.025),
                        ),
                      ),
                    ),
                    for (var task in controller.tasks!)
                      GestureDetector(
                        onTap: () async {
                          if (Navigator.of(context).canPop()) {
                            Navigator.of(context).pop();
                          }
                          await 0.3.delay();
                          setState(() {
                            p = 0;
                            isScheduler = true;
                            add = false;
                            myId = task.fields['id'].toString();
                            h = int.parse(
                                task.fields['nclock'].toString().split(':')[0]);
                            m = int.parse(task.fields['nclock']
                                .toString()
                                .replaceAll(RegExp(r'\s?(AM|PM)'), '')
                                .split(':')[1]);
                            isAM = task.fields['nclock'].contains('AM')
                                ? true
                                : false;
                            days = [];
                            if (task.fields['wday']
                                .toString()
                                .contains('Sat')) {
                              days.add('سبت');
                            }
                            if (task.fields['wday']
                                .toString()
                                .contains('Sun')) {
                              days.add('أحد');
                            }
                            if (task.fields['wday']
                                .toString()
                                .contains('Mon')) {
                              days.add('إثنين');
                            }
                            if (task.fields['wday']
                                .toString()
                                .contains('Tue')) {
                              days.add('ثلاثاء');
                            }
                            if (task.fields['wday']
                                .toString()
                                .contains('Wed')) {
                              days.add('اربعاء');
                            }
                            if (task.fields['wday']
                                .toString()
                                .contains('Thu')) {
                              days.add('خميس');
                            }
                            if (task.fields['wday']
                                .toString()
                                .contains('Fri')) {
                              days.add('جمعة');
                            }
                            if (days.length == 7) {
                              allday = true;
                            } else {
                              allday = false;
                            }
                            re = task.fields['re'].toString() == 'ON'
                                ? true
                                : false;

                            controller.addRoutine =
                                json.decode(task.fields['route']);
                            isSetting = true;
                            isShortcut = false;
                            isTask = true;
                            isWords = false;
                          });
                          pageController.jumpToPage(p);
                        },
                        child: containerIconsOption(
                            radius: 10,
                            margin: EdgeInsets.all(controller.sized * 0.005),
                            padding: EdgeInsets.all(controller.sized * 0.002),
                            content: Directionality(
                              textDirection: TextDirection.rtl,
                              child: Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: controller.sizedWidth * 0.02),
                                  child: Column(
                                    children: [
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          Text(
                                            task.fields['nclock']
                                                .replaceAll(
                                                    RegExp(r'\s?(AM|PM)'), '')
                                                .trim(),
                                            textDirection: TextDirection.rtl,
                                            style: TextStyle(
                                              color: AppColors.warningColor,
                                              fontSize: controller.sized * 0.03,
                                            ),
                                          ),
                                          SizedBox(
                                            width:
                                                controller.sizedWidth * 0.005,
                                          ),
                                          Expanded(
                                            child: Padding(
                                              padding: EdgeInsets.only(
                                                  bottom:
                                                      controller.sizedHight *
                                                          0.005,
                                                  right: controller.sizedHight *
                                                      0.005),
                                              child: Text(
                                                task.fields['nclock']
                                                        .contains('AM')
                                                    ? 'صباحاً'
                                                    : 'مسائاً',
                                                textDirection:
                                                    TextDirection.rtl,
                                                style: TextStyle(
                                                    color: AppColors.textColor2,
                                                    fontSize: controller.sized *
                                                        0.012,
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ),
                                            ),
                                          ),
                                          switchStyle(
                                              onChanged: (val) async {
                                                await conn.query(
                                                    "UPDATE ADevice SET state = ? WHERE id = ?",
                                                    [
                                                      val! ? 'ON' : 'STOP',
                                                      task.fields['id']
                                                          .toString()
                                                    ]);
                                                final builder =
                                                    MqttClientPayloadBuilder();
                                                builder.addString('1');
                                                client.publishMessage(
                                                    'edit',
                                                    MqttQos.atLeastOnce,
                                                    builder.payload!);
                                              },
                                              value:
                                                  task.fields['state'] == 'ON'
                                                      ? true
                                                      : false,
                                              size: controller.sized * 0.0007)
                                        ],
                                      ),
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal:
                                                controller.sizedWidth * 0.01),
                                        child: Row(
                                          children: [
                                            Text(
                                              'سبت',
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: task.fields['wday']
                                                          .toString()
                                                          .contains('Sat')
                                                      ? AppColors.warningColor
                                                      : AppColors.textColor2
                                                          .withOpacity(0.2),
                                                  fontSize:
                                                      controller.sized * 0.009,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            SizedBox(
                                              width:
                                                  controller.sizedWidth * 0.02,
                                            ),
                                            Text(
                                              'أحد',
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: task.fields['wday']
                                                          .contains("Sun")
                                                      ? AppColors.warningColor
                                                      : AppColors.textColor2
                                                          .withOpacity(0.2),
                                                  fontSize:
                                                      controller.sized * 0.009,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            SizedBox(
                                              width:
                                                  controller.sizedWidth * 0.02,
                                            ),
                                            Text(
                                              'إثنين',
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: task.fields['wday']
                                                          .contains("Mon")
                                                      ? AppColors.warningColor
                                                      : AppColors.textColor2
                                                          .withOpacity(0.2),
                                                  fontSize:
                                                      controller.sized * 0.009,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            SizedBox(
                                              width:
                                                  controller.sizedWidth * 0.02,
                                            ),
                                            Text(
                                              'ثلاثاء',
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: task.fields['wday']
                                                          .contains("Tue")
                                                      ? AppColors.warningColor
                                                      : AppColors.textColor2
                                                          .withOpacity(0.2),
                                                  fontSize:
                                                      controller.sized * 0.009,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            SizedBox(
                                              width:
                                                  controller.sizedWidth * 0.02,
                                            ),
                                            Text(
                                              'اربعاء',
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: task.fields['wday']
                                                          .contains("Wed")
                                                      ? AppColors.warningColor
                                                      : AppColors.textColor2
                                                          .withOpacity(0.2),
                                                  fontSize:
                                                      controller.sized * 0.009,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            SizedBox(
                                              width:
                                                  controller.sizedWidth * 0.02,
                                            ),
                                            Text(
                                              'خميس',
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: task.fields['wday']
                                                          .contains("Thu")
                                                      ? AppColors.warningColor
                                                      : AppColors.textColor2
                                                          .withOpacity(0.2),
                                                  fontSize:
                                                      controller.sized * 0.009,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            SizedBox(
                                              width:
                                                  controller.sizedWidth * 0.02,
                                            ),
                                            Text(
                                              'جمعة',
                                              textDirection: TextDirection.rtl,
                                              style: TextStyle(
                                                  color: task.fields['wday']
                                                          .contains("Fri")
                                                      ? AppColors.warningColor
                                                      : AppColors.textColor2
                                                          .withOpacity(0.2),
                                                  fontSize:
                                                      controller.sized * 0.009,
                                                  fontWeight: FontWeight.bold),
                                            ),
                                            SizedBox(
                                              width:
                                                  controller.sizedWidth * 0.05,
                                            ),
                                            Icon(
                                              Icons.refresh_rounded,
                                              size: controller.sized * 0.018,
                                              color: task.fields['re'] == 'ON'
                                                  ? AppColors.warningColor
                                                  : AppColors.textColor2
                                                      .withOpacity(0.2),
                                            )
                                          ],
                                        ),
                                      )
                                    ],
                                  )),
                            )),
                      ),
                  ],
                ))))

        // btnCancelOnPress:
        //     () {},
        ).show();
  }
}
