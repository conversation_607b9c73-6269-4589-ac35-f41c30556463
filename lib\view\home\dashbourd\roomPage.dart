import 'package:flutter/material.dart';
import 'package:zaen/shared/components/components.dart';
import 'package:zaen/view/home/<USER>/routine.dart';
import 'package:zaen/shared/themes/app_colors.dart';

// HomeController controller = Get.find();

Widget Room({
  setState1,
}) {
  bool? roomState;
  if (controller.addRoutine.containsKey(roomData['id']) &&
      (controller.addRoutine[roomData['id']] == true ||
          controller.addRoutine[roomData['id']] == false)) {
    roomState = controller.addRoutine[roomData['id']];
  } else if (roomState == null) {
    roomState = roomData['state'];
  }
  return StatefulBuilder(builder: ((context, setState) {
    return Column(
      children: [
        SizedBox(
          height: controller.sizedHight * 0.015,
        ),
        Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            txtStyle(
                txt: roomData['privName']! == 'x'
                    ? 'لا يوجد اسم'
                    : roomData['privName'],
                size: controller.sized * 0.02),
            txtStyle(
                txt: roomData['pubName'],
                size: controller.sized * 0.013,
                color: AppColors.textColor2.withOpacity(0.55)),
            Divider(
              color: AppColors.textColor2.withOpacity(0.5),
            )
          ],
        ),
        containerPageOption(
          content: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              txtStyle(
                  txt:
                      ' ${roomState! ? 'تشغيل' : 'اغلاق'} ${roomData['privName']}',
                  color: AppColors.textColor2.withOpacity(0.7),
                  size: controller.sized * 0.012),
              Expanded(
                child: switchStyle(
                    size: controller.sized * 0.0011,
                    value: roomState!,
                    onChanged: (s) {
                      setState(() {
                        roomState = s;
                      });
                      if (controller.addRoutine.containsKey(roomData['id']) &&
                          (controller.addRoutine[roomData['id']] == true ||
                              controller.addRoutine[roomData['id']] == false)) {
                        controller.addRoutine[roomData['id']] = roomState;
                      }
                    }),
              ),
              IconButton(
                  onPressed: () {
                    if (controller.addRoutine.containsKey('home')) {
                      controller.addRoutine = {};
                    }
                    if (controller.addRoutine.containsKey(roomData['id']) ==
                            false ||
                        (controller.addRoutine.containsKey(roomData['id']) &&
                            controller.addRoutine[roomData['id']] != true &&
                            controller.addRoutine[roomData['id']] != false)) {
                      controller.addRoutine[roomData['id']] = roomState;
                    } else {
                      controller.addRoutine.remove(roomData['id']);
                    }
                    setState1(
                      () {
                        controller.addRoutine;
                      },
                    );
                  },
                  iconSize: controller.sized * 0.03,
                  color: controller.addRoutine.containsKey(roomData['id']) &&
                          (controller.addRoutine[roomData['id']] == true ||
                              controller.addRoutine[roomData['id']] == false)
                      ? AppColors.primaryColor
                      : Colors.amber,
                  icon: iconStyle(
                    icon: controller.addRoutine.containsKey(roomData['id']) &&
                            (controller.addRoutine[roomData['id']] == true ||
                                controller.addRoutine[roomData['id']] == false)
                        ? Icons.check_circle_rounded
                        : Icons.add_circle_outline_rounded,
                    color: controller.addRoutine.containsKey(roomData['id']) &&
                            (controller.addRoutine[roomData['id']] == true ||
                                controller.addRoutine[roomData['id']] == false)
                        ? AppColors.primaryColor
                        : AppColors.warningColor,
                  ))
            ],
          ),
        ),
        SizedBox(
          height: controller.sizedHight * 0.01,
        ),
        Container(
          margin: EdgeInsets.symmetric(
              horizontal: controller.sizedWidth * 0.02,
              vertical: controller.sizedHight * 0.01),
          padding: EdgeInsets.symmetric(
              horizontal: controller.sizedWidth * 0.02,
              vertical: controller.sizedHight * 0.02),
          decoration: BoxDecoration(
              color: AppColors.textColor2.withOpacity(0.05),
              borderRadius: BorderRadius.circular(17)),
          height: controller.sizedHight * 0.45,
          child: ListView(
            shrinkWrap: true,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  txtStyle(
                      txt: ' الملحقات',
                      size: controller.sized * 0.012,
                      color: AppColors.textColor2.withOpacity(0.65)),
                ],
              ),
              for (var d in roomData['devices'].values)
                if (d['device'].toString().contains('ZAIN') == false)
                  GestureDetector(
                    onTap: () async {
                      print(d);
                      setState1(() {
                        p = 3;
                        device = d;

                        if (device['state'] == null) {
                          device['state'] = false;
                        }
                      });

                      await pageController.nextPage(
                          duration: Duration(milliseconds: 350),
                          curve: Curves.slowMiddle);
                    },
                    child: Container(
                      margin: EdgeInsets.symmetric(
                          horizontal: controller.sizedWidth * 0.04,
                          vertical: controller.sizedHight * 0.01),
                      decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(25)),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          iconStyle(
                            icon: d['device'] == 'SWITCH'
                                ? Icons.power_rounded
                                : d['device'] == 'TV'
                                    ? Icons.tv_rounded
                                    : Icons.ac_unit_rounded,
                            size: controller.sized * 0.025,
                            color: AppColors.warningColor,
                          ),
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.only(
                                  right: controller.sizedWidth * 0.03,
                                  top: controller.sizedHight * 0.005),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  txtStyle(
                                    txt: d['device'] == 'SWITCH'
                                        ? 'مفاتيح : ' +
                                            (d['priv'].split('_')[0] == 'x'
                                                ? 'لا يوجد اسم'
                                                : d['priv'].split('_')[0])
                                        : (d['device'] == 'TV'
                                                ? 'تلفاز : '
                                                : 'مكيف : ') +
                                            (d['priv'] == 'x'
                                                ? 'لا يوجد اسم'
                                                : d['priv']),
                                  ),
                                  Divider(
                                    color:
                                        AppColors.textColor2.withOpacity(0.5),
                                  )
                                ],
                              ),
                            ),
                          ),
                          iconStyle(
                            icon: Icons.menu_open_rounded,
                            size: controller.sized * 0.025,
                            color: AppColors.textColor2.withOpacity(0.8),
                          ),
                        ],
                      ),
                    ),
                  )
            ],
          ),
        ),
        SizedBox(
          height: controller.sizedHight * 0.01,
        ),
      ],
    );
  }));
}
