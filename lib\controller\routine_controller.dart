import 'package:get/get.dart';
import 'package:mysql1/mysql1.dart';
import 'base_controller.dart';

/// كونترولر إدارة الروتين والمهام
class RoutineController extends BaseController {
  // بيانات الروتين
  Map addRoutine = {};
  List routines = [];
  Map alarms = {};
  Results? tasks;
  Results? routineWords;

  /// إضافة روتين جديد
  void addNewRoutine(String routineId, Map routineData) {
    addRoutine[routineId] = routineData;
    update();
  }

  /// حذف روتين
  void removeRoutine(String routineId) {
    addRoutine.remove(routineId);
    update();
  }

  /// تحديث روتين موجود
  void updateRoutine(String routineId, Map newData) {
    if (addRoutine.containsKey(routineId)) {
      addRoutine[routineId].addAll(newData);
      update();
    }
  }

  /// الحصول على معلومات روتين
  Map? getRoutineInfo(String routineId) {
    return addRoutine[routineId];
  }

  /// تفعيل/إلغاء تفعيل روتين
  void toggleRoutine(String routineId, bool isActive) {
    if (addRoutine.containsKey(routineId)) {
      addRoutine[routineId]['active'] = isActive;
      update();
    }
  }

  /// الحصول على جميع الروتينات النشطة
  List getActiveRoutines() {
    List activeRoutines = [];
    for (var entry in addRoutine.entries) {
      if (entry.value['active'] == true) {
        activeRoutines.add({
          'id': entry.key,
          'data': entry.value,
        });
      }
    }
    return activeRoutines;
  }

  /// إضافة مهمة جديدة
  void addTask(String taskId, Map taskData) {
    // منطق إضافة المهمة
    update();
  }

  /// حذف مهمة
  void removeTask(String taskId) {
    // منطق حذف المهمة
    update();
  }

  /// تحديث حالة مهمة
  void updateTaskStatus(String taskId, String status) {
    // منطق تحديث حالة المهمة
    update();
  }

  /// إضافة منبه جديد
  void addAlarm(String alarmId, Map alarmData) {
    alarms[alarmId] = alarmData;
    update();
  }

  /// حذف منبه
  void removeAlarm(String alarmId) {
    alarms.remove(alarmId);
    update();
  }

  /// تحديث منبه
  void updateAlarm(String alarmId, Map newData) {
    if (alarms.containsKey(alarmId)) {
      alarms[alarmId].addAll(newData);
      update();
    }
  }

  /// تفعيل/إلغاء تفعيل منبه
  void toggleAlarm(String alarmId, bool isActive) {
    if (alarms.containsKey(alarmId)) {
      alarms[alarmId]['active'] = isActive;
      update();
    }
  }

  /// الحصول على جميع المنبهات النشطة
  List getActiveAlarms() {
    List activeAlarms = [];
    for (var entry in alarms.entries) {
      if (entry.value['active'] == true) {
        activeAlarms.add({
          'id': entry.key,
          'data': entry.value,
        });
      }
    }
    return activeAlarms;
  }

  /// الحصول على عدد الروتينات
  int getRoutinesCount() {
    return addRoutine.length;
  }

  /// الحصول على عدد الروتينات النشطة
  int getActiveRoutinesCount() {
    return getActiveRoutines().length;
  }

  /// الحصول على عدد المنبهات
  int getAlarmsCount() {
    return alarms.length;
  }

  /// الحصول على عدد المنبهات النشطة
  int getActiveAlarmsCount() {
    return getActiveAlarms().length;
  }

  /// تشغيل روتين
  Future<void> executeRoutine(String routineId) async {
    if (addRoutine.containsKey(routineId)) {
      // منطق تشغيل الروتين
      print('تشغيل الروتين: $routineId');
    }
  }

  /// إيقاف جميع الروتينات
  void stopAllRoutines() {
    for (var routineId in addRoutine.keys) {
      toggleRoutine(routineId, false);
    }
  }

  /// إيقاف جميع المنبهات
  void stopAllAlarms() {
    for (var alarmId in alarms.keys) {
      toggleAlarm(alarmId, false);
    }
  }
}
