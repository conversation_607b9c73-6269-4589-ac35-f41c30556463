import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import '../../../controller/controller.dart';

// متغير controller مشترك
final HomeController controller = HomeController();

/// ويدجت مخصص لـ TextFormField مع تصميم متقدم
/// 
/// يدعم:
/// - تخصيص النص والألوان
/// - التحقق من صحة البيانات
/// - أيقونات مخصصة
/// - تصميم متجاوب مع الثيم
/// 
/// مثال على الاستخدام:
/// ```dart
/// customTextFormField(
///   textController: _nameController,
///   hintText: 'أدخل اسمك',
///   labelText: 'الاسم',
///   prefixIcon: Icons.person,
///   validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
/// )
/// ```
Widget customTextFormField({
  required TextEditingController textController,
  String? hintText,
  String? labelText,
  IconData? prefixIcon,
  IconData? suffixIcon,
  VoidCallback? onSuffixIconPressed,
  String? Function(String?)? validator,
  TextInputType keyboardType = TextInputType.text,
  bool obscureText = false,
  int maxLines = 1,
  int? maxLength,
  bool enabled = true,
  Color? fillColor,
  Color? borderColor,
  double borderRadius = 12.0,
  EdgeInsets? contentPadding,
}) {
  return TextFormField(
    controller: textController,
    validator: validator,
    keyboardType: keyboardType,
    obscureText: obscureText,
    maxLines: maxLines,
    maxLength: maxLength,
    enabled: enabled,
    textDirection: TextDirection.rtl,
    style: TextStyle(
      color: AppColors.textPrimary,
      fontSize: controller.sized * 0.014,
    ),
    decoration: InputDecoration(
      hintText: hintText,
      labelText: labelText,
      hintStyle: TextStyle(
        color: AppColors.textSecondary.withOpacity(0.6),
        fontSize: controller.sized * 0.013,
      ),
      labelStyle: TextStyle(
        color: AppColors.textSecondary,
        fontSize: controller.sized * 0.013,
      ),
      prefixIcon: prefixIcon != null
          ? Icon(
              prefixIcon,
              color: AppColors.textSecondary,
              size: controller.sized * 0.02,
            )
          : null,
      suffixIcon: suffixIcon != null
          ? IconButton(
              icon: Icon(
                suffixIcon,
                color: AppColors.textSecondary,
                size: controller.sized * 0.02,
              ),
              onPressed: onSuffixIconPressed,
            )
          : null,
      filled: true,
      fillColor: fillColor ?? AppColors.inputFill,
      contentPadding: contentPadding ??
          EdgeInsets.symmetric(
            horizontal: controller.sizedWidth * 0.04,
            vertical: controller.sizedHight * 0.015,
          ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: BorderSide(
          color: borderColor ?? AppColors.inputBorder,
          width: 1.0,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: BorderSide(
          color: borderColor ?? AppColors.inputBorder,
          width: 1.0,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: BorderSide(
          color: AppColors.primary,
          width: 2.0,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: BorderSide(
          color: AppColors.error,
          width: 1.0,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadius),
        borderSide: BorderSide(
          color: AppColors.error,
          width: 2.0,
        ),
      ),
    ),
  );
}

/// ويدجت مخصص لـ TextField يتناسب مع الوضعين الداكن والفاتح
Widget customTextField({
  required TextEditingController textController,
  String? hintText,
  bool obscureText = false,
  IconData? prefixIcon,
  IconData? suffixIcon,
  VoidCallback? onSuffixIconPressed,
  TextInputType keyboardType = TextInputType.text,
  int maxLines = 1,
  Function(String)? onChanged,
  Function(String)? onSubmitted,
}) {
  return Container(
    margin: EdgeInsets.symmetric(vertical: controller.sizedHight * 0.01),
    child: TextField(
      controller: textController,
      obscureText: obscureText,
      keyboardType: keyboardType,
      maxLines: maxLines,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      textDirection: TextDirection.rtl,
      style: TextStyle(
        color: AppColors.textPrimary,
        fontSize: controller.sized * 0.014,
      ),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(
          color: AppColors.textSecondary.withOpacity(0.6),
          fontSize: controller.sized * 0.013,
        ),
        prefixIcon: prefixIcon != null
            ? Icon(
                prefixIcon,
                color: AppColors.textSecondary,
                size: controller.sized * 0.02,
              )
            : null,
        suffixIcon: suffixIcon != null
            ? IconButton(
                icon: Icon(
                  suffixIcon,
                  color: AppColors.textSecondary,
                  size: controller.sized * 0.02,
                ),
                onPressed: onSuffixIconPressed,
              )
            : null,
        filled: true,
        fillColor: AppColors.inputFill,
        contentPadding: EdgeInsets.symmetric(
          horizontal: controller.sizedWidth * 0.04,
          vertical: controller.sizedHight * 0.015,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.inputBorder,
            width: 1.0,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.inputBorder,
            width: 1.0,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: AppColors.primary,
            width: 2.0,
          ),
        ),
      ),
    ),
  );
}

/// ويدجت مبسط لحقل النص مع التنسيق الأساسي
Widget simpleTextFormField({
  required TextEditingController textController,
  String? hintText,
  int? maxLength,
  TextInputType keyboardType = TextInputType.text,
}) {
  return TextFormField(
    controller: textController,
    keyboardType: keyboardType,
    maxLength: maxLength,
    textDirection: TextDirection.rtl,
    decoration: InputDecoration(
      hintText: hintText,
      border: OutlineInputBorder(),
    ),
  );
}

/// ويدجت مبسط لحقل كلمة المرور
Widget passwordTextFormField({
  required TextEditingController textController,
  String? hintText,
  bool obscureText = true,
  VoidCallback? onToggleVisibility,
}) {
  return TextFormField(
    controller: textController,
    obscureText: obscureText,
    textDirection: TextDirection.rtl,
    decoration: InputDecoration(
      hintText: hintText ?? 'كلمة المرور',
      suffixIcon: IconButton(
        icon: Icon(obscureText ? Icons.visibility : Icons.visibility_off),
        onPressed: onToggleVisibility,
      ),
      border: OutlineInputBorder(),
    ),
  );
}
