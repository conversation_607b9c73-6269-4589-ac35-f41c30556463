import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:zaen/controller/controller.dart';

/// مساعدات الإعدادات
class SettingsHelpers {
  /// التعامل مع اختيار الأصول
  static Future<void> handleAssetSelection(HomeController controller) async {
    try {
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      Map<String, dynamic> manifestMap = await json.decode(manifestContent);
      
      List imagePaths = await manifestMap.keys
          .where((String key) => key.contains('images/places/${controller.homeType.value}/'))
          .toList();
      
      print(imagePaths);
      var item = imagePaths.indexOf(controller.homeImage.value);
      
      if (item == -1) {
        item = 0;
      }
      
      // يمكن إضافة منطق إضافي هنا لاختيار الصورة
      print('تم اختيار الصورة: ${imagePaths[item]}');
    } catch (e) {
      print('خطأ في اختيار الأصول: $e');
    }
  }

  /// التعامل مع إجراء التدوير
  static Future<void> handleRollAction(HomeController controller) async {
    try {
      // منطق التدوير
      print('تم تنفيذ إجراء التدوير');
    } catch (e) {
      print('خطأ في إجراء التدوير: $e');
    }
  }

  /// التعامل مع إجراء الكاميرا
  static Future<void> handleCameraAction(HomeController controller) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(source: ImageSource.camera);
      
      if (image != null) {
        // تحديث صورة المنزل
        controller.homeImage.value = image.path;
        controller.update();
        print('تم التقاط صورة جديدة: ${image.path}');
      }
    } catch (e) {
      print('خطأ في التقاط الصورة: $e');
    }
  }

  /// التعامل مع تعديل الأسماء
  static void handleEditNames(HomeController controller, String? newName) {
    if (newName != null && newName.isNotEmpty) {
      controller.home.value = newName;
      controller.update();
      print('تم تحديث اسم المنزل إلى: $newName');
    }
  }

  /// التعامل مع تعديل الاسم الخاص
  static void handleEditPrivName(HomeController controller, bool? isPrivate, String? name) {
    if (name != null && name.isNotEmpty) {
      // منطق تحديث الاسم الخاص
      print('تم تحديث الاسم الخاص إلى: $name (خاص: $isPrivate)');
    }
  }

  /// الحصول على قائمة الصور المتاحة
  static Future<List<String>> getAvailableImages(String homeType) async {
    try {
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      Map<String, dynamic> manifestMap = await json.decode(manifestContent);
      
      List<String> imagePaths = manifestMap.keys
          .where((String key) => key.contains('images/places/$homeType/'))
          .cast<String>()
          .toList();
      
      return imagePaths;
    } catch (e) {
      print('خطأ في الحصول على الصور المتاحة: $e');
      return [];
    }
  }

  /// التحقق من صحة اسم المنزل
  static bool isValidHomeName(String name) {
    return name.isNotEmpty && name.trim().length >= 2;
  }

  /// تنظيف اسم المنزل
  static String cleanHomeName(String name) {
    return name.trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// الحصول على أنواع المنازل المتاحة
  static List<String> getAvailableHomeTypes() {
    return [
      'apartment',
      'house',
      'villa',
      'office',
      'studio',
    ];
  }

  /// الحصول على نص نوع المنزل
  static String getHomeTypeText(String homeType) {
    switch (homeType.toLowerCase()) {
      case 'apartment':
        return 'شقة';
      case 'house':
        return 'منزل';
      case 'villa':
        return 'فيلا';
      case 'office':
        return 'مكتب';
      case 'studio':
        return 'استوديو';
      default:
        return 'غير محدد';
    }
  }
}
