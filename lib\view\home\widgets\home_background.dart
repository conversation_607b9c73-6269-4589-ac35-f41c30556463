import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/themes/app_colors.dart';

/// ويدجت خلفية الصفحة الرئيسية
class HomeBackground extends StatelessWidget {
  final HomeController controller;

  const HomeBackground({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // الصورة الخلفية
        _buildBackgroundImage(context),
        
        // فلتر الضبابية
        _buildBlurFilter(),
        
        // التدرج اللوني
        _buildGradientOverlay(),
      ],
    );
  }

  /// بناء الصورة الخلفية
  Widget _buildBackgroundImage(BuildContext context) {
    return controller.homeImage.value.contains('com.example.zaen')
        ? Image.file(
            File(controller.homeImage.value),
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            fit: BoxFit.cover,
            filterQuality: FilterQuality.high,
          )
        : Image.asset(
            controller.homeImage.value,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          );
  }

  /// بناء فلتر الضبابية
  Widget _buildBlurFilter() {
    return BackdropFilter(
      blendMode: BlendMode.srcIn,
      filter: ImageFilter.blur(
        sigmaX: 40,
        sigmaY: 40,
      ),
      child: Container(
        color: AppColors.backgroundColor.withOpacity(0.3),
      ),
    );
  }

  /// بناء التدرج اللوني
  Widget _buildGradientOverlay() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            AppColors.backgroundColor.withOpacity(0.2),
            AppColors.backgroundColor.withOpacity(0.5),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
    );
  }
}
