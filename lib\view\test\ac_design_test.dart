import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:zaen/controller/controller.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import 'package:zaen/view/home/<USER>/favorite/ac.dart';

class ACDesignTestPage extends StatefulWidget {
  const ACDesignTestPage({Key? key}) : super(key: key);

  @override
  State<ACDesignTestPage> createState() => _ACDesignTestPageState();
}

class _ACDesignTestPageState extends State<ACDesignTestPage> {
  HomeController controller = Get.put(HomeController(), permanent: true);

  // Mock device data for testing
  Map<String, dynamic> mockDevice = {
    'id': 'ac_001',
    'priv': 'مكيف الصالة_غرفة المعيشة',
    'state': true,
    'degree': 24,
    'speed': 2,
    'type': 'تبريد',
    'swing': false,
  };

  String mockRoom = 'living_room';

  @override
  void initState() {
    super.initState();
    // Initialize mock data
    controller.rooms = {
      mockRoom: {
        'privName': 'غرفة المعيشة',
        'devices': {
          mockDevice['id']: mockDevice,
        }
      }
    };
    controller.sizedWidth = 400;
    controller.sizedHight = 800;
    controller.sized = 1200;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      appBar: AppBar(
        title: const Text('تصميم المكيف الجديد'),
        backgroundColor: AppColors.surface,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              'التصميم الاحترافي الجديد للمكيف',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'تصميم حديث وأنيق مع تحسينات في تجربة المستخدم',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 24),

            // AC Widget Container
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.shadow.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: AC(
                  device: mockDevice,
                  room: mockRoom,
                  switchState: (bool? state) {
                    setState(() {
                      mockDevice['state'] = state ?? false;
                    });
                    print('Switch state changed to: $state');
                  },
                  sliderState: (double? value) {
                    setState(() {
                      mockDevice['degree'] = value?.toInt() ?? 24;
                    });
                    print('Temperature changed to: ${value?.toInt()}°C');
                  },
                  acTypeState: (int? type) {
                    setState(() {
                      mockDevice['type'] = type == 0
                          ? 'تدفئه'
                          : type == 1
                              ? 'تبريد'
                              : 'مروحه';
                    });
                    print('AC type changed to: ${mockDevice['type']}');
                  },
                  acSwingState: () {
                    setState(() {
                      mockDevice['swing'] = !mockDevice['swing'];
                    });
                    print('Swing state changed to: ${mockDevice['swing']}');
                  },
                  acSpeedsStateLeft: () {
                    setState(() {
                      if (mockDevice['speed'] > 1) {
                        mockDevice['speed']--;
                      }
                    });
                    print('Speed decreased to: ${mockDevice['speed']}');
                  },
                  acSpeedsStateRight: () {
                    setState(() {
                      if (mockDevice['speed'] < 4) {
                        mockDevice['speed']++;
                      }
                    });
                    print('Speed increased to: ${mockDevice['speed']}');
                  },
                  acRun: () {
                    print('AC Run button pressed');
                    // Show confirmation
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content:
                            const Text('تم تشغيل المكيف بالإعدادات الحالية'),
                        backgroundColor: AppColors.success,
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Features List
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.border.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المميزات الجديدة:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildFeatureItem('🎨', 'تصميم احترافي حديث'),
                  _buildFeatureItem('🌡️', 'عرض درجة الحرارة بشكل واضح'),
                  _buildFeatureItem('🎛️', 'أزرار تحكم متطورة'),
                  _buildFeatureItem('💨', 'تحكم محسن في سرعة المروحة'),
                  _buildFeatureItem('🔄', 'أزرار وضع التشغيل التفاعلية'),
                  _buildFeatureItem('✨', 'تأثيرات بصرية وانتقالات سلسة'),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Current Settings Display
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withOpacity(0.1),
                    AppColors.surface,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'الإعدادات الحالية:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildSettingRow(
                      'الحالة', mockDevice['state'] ? 'مشغل' : 'مطفأ'),
                  _buildSettingRow('درجة الحرارة', '${mockDevice['degree']}°C'),
                  _buildSettingRow('النوع', mockDevice['type']),
                  _buildSettingRow('السرعة', 'المستوى ${mockDevice['speed']}'),
                  _buildSettingRow(
                      'التأرجح', mockDevice['swing'] ? 'مفعل' : 'معطل'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }
}
