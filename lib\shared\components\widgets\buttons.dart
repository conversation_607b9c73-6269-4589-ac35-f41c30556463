import 'package:flutter/material.dart';
import 'package:zaen/shared/themes/app_colors.dart';
import '../../../controller/controller.dart';

// متغير controller مشترك
final HomeController controller = HomeController();

/// زر الموافقة المخصص
Widget submitButtom({String text = "موافق", required Function() onPressed}) {
  return OutlinedButton(
      child: Text(
        text,
        textDirection: TextDirection.rtl,
        style: TextStyle(
            color: AppColors.textColor,
            fontSize: controller.sized * 0.02,
            fontWeight: FontWeight.bold),
      ),
      style: OutlinedButton.styleFrom(
        side: BorderSide.none,
        backgroundColor: AppColors.primaryColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(25),
          ),
        ),
      ),
      onPressed: onPressed);
}

/// زر الحذف المخصص
Widget delButtom({String text = "حذف", required Function() onPressed}) {
  return OutlinedButton(
      child: Text(
        text,
        textDirection: TextDirection.rtl,
        style: TextStyle(
            color: AppColors.white,
            fontSize: controller.sized * 0.02,
            fontWeight: FontWeight.bold),
      ),
      style: OutlinedButton.styleFrom(
        side: BorderSide.none,
        backgroundColor: AppColors.error,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(25),
          ),
        ),
      ),
      onPressed: onPressed);
}
