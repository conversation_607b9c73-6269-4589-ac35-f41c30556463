import 'package:get/get.dart';
import 'base_controller.dart';

/// كونترولر إدارة المفضلة
class FavoriteController extends BaseController {
  // قائمة المفضلة
  List favorite = [];

  /// إضافة عنصر إلى المفضلة
  void addToFavorite(Map item) {
    if (!isFavorite(item['id'])) {
      favorite.add(item);
      update();
    }
  }

  /// حذف عنصر من المفضلة
  void removeFromFavorite(String itemId) {
    favorite.removeWhere((item) => item['id'] == itemId);
    update();
  }

  /// التحقق من وجود عنصر في المفضلة
  bool isFavorite(String itemId) {
    return favorite.any((item) => item['id'] == itemId);
  }

  /// تبديل حالة المفضلة لعنصر
  void toggleFavorite(Map item) {
    if (isFavorite(item['id'])) {
      removeFromFavorite(item['id']);
    } else {
      addToFavorite(item);
    }
  }

  /// الحصول على جميع عناصر المفضلة
  List getAllFavorites() {
    return List.from(favorite);
  }

  /// الحصول على عناصر المفضلة حسب النوع
  List getFavoritesByType(String type) {
    return favorite.where((item) => item['type'] == type).toList();
  }

  /// الحصول على عدد عناصر المفضلة
  int getFavoritesCount() {
    return favorite.length;
  }

  /// مسح جميع عناصر المفضلة
  void clearAllFavorites() {
    favorite.clear();
    update();
  }

  /// ترتيب المفضلة
  void reorderFavorites(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final item = favorite.removeAt(oldIndex);
    favorite.insert(newIndex, item);
    update();
  }

  /// تحديث معلومات عنصر في المفضلة
  void updateFavoriteItem(String itemId, Map newData) {
    int index = favorite.indexWhere((item) => item['id'] == itemId);
    if (index != -1) {
      favorite[index].addAll(newData);
      update();
    }
  }

  /// البحث في المفضلة
  List searchFavorites(String query) {
    if (query.isEmpty) return getAllFavorites();
    
    return favorite.where((item) {
      String name = item['name']?.toString().toLowerCase() ?? '';
      String type = item['type']?.toString().toLowerCase() ?? '';
      String room = item['room']?.toString().toLowerCase() ?? '';
      
      return name.contains(query.toLowerCase()) ||
             type.contains(query.toLowerCase()) ||
             room.contains(query.toLowerCase());
    }).toList();
  }

  /// الحصول على المفضلة حسب الغرفة
  List getFavoritesByRoom(String roomId) {
    return favorite.where((item) => item['roomId'] == roomId).toList();
  }

  /// الحصول على إحصائيات المفضلة
  Map<String, int> getFavoriteStats() {
    Map<String, int> stats = {};
    
    for (var item in favorite) {
      String type = item['type'] ?? 'unknown';
      stats[type] = (stats[type] ?? 0) + 1;
    }
    
    return stats;
  }

  /// تصدير المفضلة
  Map exportFavorites() {
    return {
      'favorites': favorite,
      'count': favorite.length,
      'exportDate': DateTime.now().toIso8601String(),
    };
  }

  /// استيراد المفضلة
  void importFavorites(Map data) {
    if (data['favorites'] != null) {
      favorite = List.from(data['favorites']);
      update();
    }
  }

  /// الحصول على آخر عناصر مضافة للمفضلة
  List getRecentFavorites({int limit = 5}) {
    List sortedFavorites = List.from(favorite);
    sortedFavorites.sort((a, b) {
      DateTime dateA = DateTime.tryParse(a['addedDate'] ?? '') ?? DateTime.now();
      DateTime dateB = DateTime.tryParse(b['addedDate'] ?? '') ?? DateTime.now();
      return dateB.compareTo(dateA);
    });
    
    return sortedFavorites.take(limit).toList();
  }
}
